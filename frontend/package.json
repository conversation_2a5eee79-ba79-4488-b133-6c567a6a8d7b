{"name": "jiangjiang-netdisk-frontend", "version": "1.0.0", "description": "江江网盘前端", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "pinia": "^2.1.6", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/node": "^20.4.5", "@types/nprogress": "^0.2.0", "@types/js-cookie": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.2.1", "@typescript-eslint/parser": "^6.2.1", "@vitejs/plugin-vue": "^4.2.3", "eslint": "^8.46.0", "eslint-plugin-vue": "^9.15.1", "typescript": "^5.1.6", "unplugin-auto-import": "^0.16.6", "unplugin-vue-components": "^0.25.1", "vite": "^4.4.7", "vue-tsc": "^1.8.5"}}