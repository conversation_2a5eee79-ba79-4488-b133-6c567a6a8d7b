// 用户相关类型
export interface User {
  id: number
  username: string
  email: string
  nickname: string
  avatar_url: string
  total_space: number
  used_space: number
  status: number
  created_at: string
  updated_at: string
}

export interface LoginForm {
  username: string
  password: string
}

export interface RegisterForm {
  username: string
  email: string
  password: string
  confirmPassword: string
  nickname?: string
}

// 文件相关类型
export interface FileItem {
  id: number
  user_id: number
  parent_id?: number
  name: string
  type: 'file' | 'folder'
  size: number
  mime_type: string
  file_hash: string
  is_deleted: boolean
  created_at: string
  updated_at: string
  deleted_at?: string
  children?: FileItem[]
}

export interface FileListParams {
  parent_id?: number
  page?: number
  page_size?: number
  order_by?: string
  order?: 'asc' | 'desc'
}

export interface FileSearchParams {
  keyword: string
  type?: 'file' | 'folder' | 'all'
  page?: number
  page_size?: number
}

export interface CreateFolderForm {
  name: string
  parent_id?: number
}

export interface RenameForm {
  name: string
}

export interface MoveForm {
  parent_id?: number
}

export interface CopyForm {
  parent_id?: number
  name?: string
}

// 分享相关类型
export interface Share {
  id: number
  file_id: number
  user_id: number
  share_code: string
  has_password: boolean
  expire_time?: string
  download_count: number
  view_count: number
  is_active: boolean
  created_at: string
  file?: FileItem
}

export interface CreateShareForm {
  file_id: number
  password?: string
  expire_days?: number
}

export interface ShareAccessForm {
  password?: string
}

// API响应类型
export interface ApiResponse<T = any> {
  code: number
  message: string
  data?: T
}

export interface PageResponse<T = any> {
  list: T[]
  total: number
  page: number
  page_size: number
}

// 存储空间信息
export interface StorageInfo {
  total_space: number
  used_space: number
  available_space: number
  usage_percentage: number
  total_space_str: string
  used_space_str: string
  available_space_str: string
}

// 面包屑导航
export interface BreadcrumbItem {
  id?: number
  name: string
  path?: string
}

// 文件上传
export interface UploadFile {
  file: File
  parent_id?: number
  progress: number
  status: 'waiting' | 'uploading' | 'success' | 'error'
  error?: string
}
