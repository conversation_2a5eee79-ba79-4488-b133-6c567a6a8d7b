import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { FileItem, BreadcrumbItem, UploadFile } from '@/types'

export const useFileStore = defineStore('file', () => {
  // 状态
  const currentPath = ref<number | undefined>(undefined) // 当前文件夹ID
  const fileList = ref<FileItem[]>([])
  const selectedFiles = ref<Set<number>>(new Set())
  const viewMode = ref<'list' | 'grid'>('list') // 视图模式
  const sortBy = ref<string>('created_at')
  const sortOrder = ref<'asc' | 'desc'>('desc')
  const searchKeyword = ref<string>('')
  const uploadQueue = ref<UploadFile[]>([])
  const breadcrumbs = ref<BreadcrumbItem[]>([{ name: '全部文件' }])
  
  // 计算属性
  const hasSelectedFiles = computed(() => selectedFiles.value.size > 0)
  const selectedFileCount = computed(() => selectedFiles.value.size)
  const isUploading = computed(() => uploadQueue.value.some(file => file.status === 'uploading'))
  const uploadProgress = computed(() => {
    const uploadingFiles = uploadQueue.value.filter(file => file.status === 'uploading')
    if (uploadingFiles.length === 0) return 0
    const totalProgress = uploadingFiles.reduce((sum, file) => sum + file.progress, 0)
    return Math.round(totalProgress / uploadingFiles.length)
  })
  
  // 设置当前路径
  const setCurrentPath = (path: number | undefined) => {
    currentPath.value = path
    clearSelection()
  }
  
  // 设置文件列表
  const setFileList = (files: FileItem[]) => {
    fileList.value = files
  }
  
  // 添加文件到列表
  const addFile = (file: FileItem) => {
    fileList.value.unshift(file)
  }
  
  // 更新文件
  const updateFile = (fileId: number, updates: Partial<FileItem>) => {
    const index = fileList.value.findIndex(file => file.id === fileId)
    if (index !== -1) {
      fileList.value[index] = { ...fileList.value[index], ...updates }
    }
  }
  
  // 删除文件
  const removeFile = (fileId: number) => {
    const index = fileList.value.findIndex(file => file.id === fileId)
    if (index !== -1) {
      fileList.value.splice(index, 1)
    }
    selectedFiles.value.delete(fileId)
  }
  
  // 选择文件
  const selectFile = (fileId: number) => {
    selectedFiles.value.add(fileId)
  }
  
  // 取消选择文件
  const unselectFile = (fileId: number) => {
    selectedFiles.value.delete(fileId)
  }
  
  // 切换文件选择状态
  const toggleFileSelection = (fileId: number) => {
    if (selectedFiles.value.has(fileId)) {
      selectedFiles.value.delete(fileId)
    } else {
      selectedFiles.value.add(fileId)
    }
  }
  
  // 全选/取消全选
  const toggleSelectAll = () => {
    if (selectedFiles.value.size === fileList.value.length) {
      clearSelection()
    } else {
      fileList.value.forEach(file => selectedFiles.value.add(file.id))
    }
  }
  
  // 清除选择
  const clearSelection = () => {
    selectedFiles.value.clear()
  }
  
  // 获取选中的文件
  const getSelectedFiles = () => {
    return fileList.value.filter(file => selectedFiles.value.has(file.id))
  }
  
  // 设置视图模式
  const setViewMode = (mode: 'list' | 'grid') => {
    viewMode.value = mode
  }
  
  // 设置排序
  const setSorting = (field: string, order: 'asc' | 'desc') => {
    sortBy.value = field
    sortOrder.value = order
  }
  
  // 设置搜索关键词
  const setSearchKeyword = (keyword: string) => {
    searchKeyword.value = keyword
  }
  
  // 设置面包屑导航
  const setBreadcrumbs = (crumbs: BreadcrumbItem[]) => {
    breadcrumbs.value = crumbs
  }
  
  // 添加上传任务
  const addUploadTask = (file: UploadFile) => {
    uploadQueue.value.push(file)
  }
  
  // 更新上传进度
  const updateUploadProgress = (index: number, progress: number) => {
    if (uploadQueue.value[index]) {
      uploadQueue.value[index].progress = progress
    }
  }
  
  // 更新上传状态
  const updateUploadStatus = (index: number, status: UploadFile['status'], error?: string) => {
    if (uploadQueue.value[index]) {
      uploadQueue.value[index].status = status
      if (error) {
        uploadQueue.value[index].error = error
      }
    }
  }
  
  // 清除上传队列
  const clearUploadQueue = () => {
    uploadQueue.value = uploadQueue.value.filter(file => file.status === 'uploading')
  }
  
  // 移除上传任务
  const removeUploadTask = (index: number) => {
    uploadQueue.value.splice(index, 1)
  }
  
  return {
    // 状态
    currentPath,
    fileList,
    selectedFiles,
    viewMode,
    sortBy,
    sortOrder,
    searchKeyword,
    uploadQueue,
    breadcrumbs,
    
    // 计算属性
    hasSelectedFiles,
    selectedFileCount,
    isUploading,
    uploadProgress,
    
    // 方法
    setCurrentPath,
    setFileList,
    addFile,
    updateFile,
    removeFile,
    selectFile,
    unselectFile,
    toggleFileSelection,
    toggleSelectAll,
    clearSelection,
    getSelectedFiles,
    setViewMode,
    setSorting,
    setSearchKeyword,
    setBreadcrumbs,
    addUploadTask,
    updateUploadProgress,
    updateUploadStatus,
    clearUploadQueue,
    removeUploadTask,
  }
})
