<template>
  <div class="files-page">
    <!-- 工具栏 -->
    <div class="toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="showCreateFolderDialog">
          新建文件夹
        </el-button>
        <el-upload
          ref="uploadRef"
          :action="uploadAction"
          :headers="uploadHeaders"
          :data="uploadData"
          :on-success="handleUploadSuccess"
          :on-error="handleUploadError"
          :on-progress="handleUploadProgress"
          :before-upload="beforeUpload"
          :show-file-list="false"
          multiple
        >
          <el-button :icon="Upload">上传文件</el-button>
        </el-upload>
        
        <el-dropdown v-if="fileStore.hasSelectedFiles" @command="handleBatchCommand">
          <el-button :icon="More">
            批量操作 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="download">
                <el-icon><Download /></el-icon>
                下载
              </el-dropdown-item>
              <el-dropdown-item command="move">
                <el-icon><FolderOpened /></el-icon>
                移动
              </el-dropdown-item>
              <el-dropdown-item command="copy">
                <el-icon><CopyDocument /></el-icon>
                复制
              </el-dropdown-item>
              <el-dropdown-item command="delete" divided>
                <el-icon><Delete /></el-icon>
                删除
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
      
      <div class="toolbar-right">
        <el-button-group class="view-toggle">
          <el-button
            :type="fileStore.viewMode === 'list' ? 'primary' : ''"
            :icon="List"
            @click="fileStore.setViewMode('list')"
          />
          <el-button
            :type="fileStore.viewMode === 'grid' ? 'primary' : ''"
            :icon="Grid"
            @click="fileStore.setViewMode('grid')"
          />
        </el-button-group>
        
        <el-dropdown @command="handleSortCommand">
          <el-button :icon="Sort">
            排序 <el-icon class="el-icon--right"><arrow-down /></el-icon>
          </el-button>
          <template #dropdown>
            <el-dropdown-menu>
              <el-dropdown-item command="name">名称</el-dropdown-item>
              <el-dropdown-item command="size">大小</el-dropdown-item>
              <el-dropdown-item command="created_at">创建时间</el-dropdown-item>
              <el-dropdown-item command="updated_at">修改时间</el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>
    
    <!-- 文件列表 -->
    <div class="file-content" v-loading="loading">
      <!-- 列表视图 -->
      <div v-if="fileStore.viewMode === 'list'" class="list-view">
        <div class="list-header">
          <div class="header-cell checkbox-cell">
            <el-checkbox
              :model-value="isAllSelected"
              :indeterminate="isIndeterminate"
              @change="handleSelectAll"
            />
          </div>
          <div class="header-cell name-cell">名称</div>
          <div class="header-cell size-cell">大小</div>
          <div class="header-cell time-cell">修改时间</div>
          <div class="header-cell action-cell">操作</div>
        </div>
        
        <div class="list-body">
          <div
            v-for="file in fileList"
            :key="file.id"
            class="file-item"
            :class="{ selected: fileStore.selectedFiles.has(file.id) }"
            @click="handleFileClick(file)"
            @dblclick="handleFileDoubleClick(file)"
          >
            <div class="item-cell checkbox-cell">
              <el-checkbox
                :model-value="fileStore.selectedFiles.has(file.id)"
                @change="() => fileStore.toggleFileSelection(file.id)"
                @click.stop
              />
            </div>
            <div class="item-cell name-cell">
              <div class="file-info">
                <el-icon class="file-icon" :style="{ color: getFileTypeColor(file.name, file.type === 'folder') }">
                  <component :is="getFileIconComponent(file.name, file.type === 'folder')" />
                </el-icon>
                <span class="file-name">{{ file.name }}</span>
              </div>
            </div>
            <div class="item-cell size-cell">
              {{ file.type === 'folder' ? '-' : formatFileSize(file.size) }}
            </div>
            <div class="item-cell time-cell">
              {{ formatTime(file.updated_at) }}
            </div>
            <div class="item-cell action-cell">
              <el-dropdown @command="(command) => handleFileCommand(command, file)" @click.stop>
                <el-button :icon="More" text />
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item v-if="file.type === 'file'" command="download">
                      <el-icon><Download /></el-icon>
                      下载
                    </el-dropdown-item>
                    <el-dropdown-item command="rename">
                      <el-icon><Edit /></el-icon>
                      重命名
                    </el-dropdown-item>
                    <el-dropdown-item command="move">
                      <el-icon><FolderOpened /></el-icon>
                      移动
                    </el-dropdown-item>
                    <el-dropdown-item command="copy">
                      <el-icon><CopyDocument /></el-icon>
                      复制
                    </el-dropdown-item>
                    <el-dropdown-item command="share" v-if="file.type === 'file'">
                      <el-icon><Share /></el-icon>
                      分享
                    </el-dropdown-item>
                    <el-dropdown-item command="delete" divided>
                      <el-icon><Delete /></el-icon>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 网格视图 -->
      <div v-else class="grid-view">
        <div
          v-for="file in fileList"
          :key="file.id"
          class="grid-item"
          :class="{ selected: fileStore.selectedFiles.has(file.id) }"
          @click="handleFileClick(file)"
          @dblclick="handleFileDoubleClick(file)"
        >
          <div class="grid-item-header">
            <el-checkbox
              :model-value="fileStore.selectedFiles.has(file.id)"
              @change="() => fileStore.toggleFileSelection(file.id)"
              @click.stop
            />
            <el-dropdown @command="(command) => handleFileCommand(command, file)" @click.stop>
              <el-button :icon="More" text size="small" />
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="file.type === 'file'" command="download">下载</el-dropdown-item>
                  <el-dropdown-item command="rename">重命名</el-dropdown-item>
                  <el-dropdown-item command="move">移动</el-dropdown-item>
                  <el-dropdown-item command="copy">复制</el-dropdown-item>
                  <el-dropdown-item command="share" v-if="file.type === 'file'">分享</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
          
          <div class="grid-item-content">
            <div class="file-preview">
              <el-icon class="preview-icon" :style="{ color: getFileTypeColor(file.name, file.type === 'folder') }">
                <component :is="getFileIconComponent(file.name, file.type === 'folder')" />
              </el-icon>
            </div>
            <div class="file-info">
              <div class="file-name" :title="file.name">{{ file.name }}</div>
              <div class="file-meta">
                <span class="file-size">{{ file.type === 'folder' ? '文件夹' : formatFileSize(file.size) }}</span>
                <span class="file-time">{{ formatTime(file.updated_at) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空状态 -->
      <div v-if="!loading && fileList.length === 0" class="empty-state">
        <el-icon class="empty-icon"><FolderOpened /></el-icon>
        <p class="empty-text">这里还没有文件</p>
        <p class="empty-desc">上传文件或创建文件夹开始使用</p>
      </div>
    </div>
    
    <!-- 分页 -->
    <div v-if="total > 0" class="pagination">
      <el-pagination
        v-model:current-page="currentPage"
        v-model:page-size="pageSize"
        :total="total"
        :page-sizes="[20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handlePageSizeChange"
        @current-change="handleCurrentPageChange"
      />
    </div>
    
    <!-- 对话框 -->
    <CreateFolderDialog
      v-model="createFolderVisible"
      :parent-id="fileStore.currentPath"
      @success="handleCreateFolderSuccess"
    />
    
    <RenameDialog
      v-model="renameVisible"
      :file="currentFile"
      @success="handleRenameSuccess"
    />
    
    <ShareDialog
      v-model="shareVisible"
      :file="currentFile"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Plus,
  Upload,
  More,
  ArrowDown,
  Download,
  FolderOpened,
  CopyDocument,
  Delete,
  List,
  Grid,
  Sort,
  Edit,
  Share,
  Folder,
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useFileStore } from '@/store/file'
import { getFileList, uploadFile, deleteFile } from '@/api/file'
import { formatFileSize, formatTime, getFileIcon, getFileTypeColor } from '@/utils'
import type { FileItem } from '@/types'
import CreateFolderDialog from '@/components/CreateFolderDialog.vue'
import RenameDialog from '@/components/RenameDialog.vue'
import ShareDialog from '@/components/ShareDialog.vue'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()
const fileStore = useFileStore()

// 响应式数据
const loading = ref(false)
const fileList = ref<FileItem[]>([])
const total = ref(0)
const currentPage = ref(1)
const pageSize = ref(20)

// 对话框状态
const createFolderVisible = ref(false)
const renameVisible = ref(false)
const shareVisible = ref(false)
const currentFile = ref<FileItem | null>(null)

// 上传相关
const uploadRef = ref()
const uploadAction = '/api/files/upload'
const uploadHeaders = computed(() => ({
  Authorization: `Bearer ${userStore.token}`,
}))
const uploadData = computed(() => ({
  parent_id: fileStore.currentPath,
}))

// 计算属性
const isAllSelected = computed(() => {
  return fileList.value.length > 0 && fileList.value.every(file => fileStore.selectedFiles.has(file.id))
})

const isIndeterminate = computed(() => {
  const selectedCount = fileStore.selectedFiles.size
  return selectedCount > 0 && selectedCount < fileList.value.length
})

// 获取文件图标组件
const getFileIconComponent = (fileName: string, isFolder: boolean) => {
  const iconName = getFileIcon(fileName, isFolder)
  const iconMap: Record<string, any> = {
    folder: Folder,
    'folder-zip': FolderOpened,
    document: Edit,
    picture: Edit,
    'video-camera': Edit,
    headphones: Edit,
  }
  return iconMap[iconName] || Edit
}

// 加载文件列表
const loadFileList = async () => {
  try {
    loading.value = true
    const params = {
      parent_id: fileStore.currentPath,
      page: currentPage.value,
      page_size: pageSize.value,
      order_by: fileStore.sortBy,
      order: fileStore.sortOrder,
    }

    const response = await getFileList(params)
    fileList.value = response.list
    total.value = response.total
    fileStore.setFileList(response.list)
  } catch (error) {
    console.error('加载文件列表失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理文件点击
const handleFileClick = (file: FileItem) => {
  fileStore.toggleFileSelection(file.id)
}

// 处理文件双击
const handleFileDoubleClick = (file: FileItem) => {
  if (file.type === 'folder') {
    // 进入文件夹
    fileStore.setCurrentPath(file.id)
    router.push({ query: { folder: file.id } })
  } else {
    // 预览文件或下载
    handleFileCommand('download', file)
  }
}

// 处理全选
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    fileList.value.forEach(file => fileStore.selectFile(file.id))
  } else {
    fileStore.clearSelection()
  }
}

// 处理文件命令
const handleFileCommand = async (command: string, file: FileItem) => {
  currentFile.value = file

  switch (command) {
    case 'download':
      if (file.type === 'file') {
        window.open(`/api/files/${file.id}/download`, '_blank')
      }
      break
    case 'rename':
      renameVisible.value = true
      break
    case 'move':
      // TODO: 实现移动功能
      ElMessage.info('移动功能开发中...')
      break
    case 'copy':
      // TODO: 实现复制功能
      ElMessage.info('复制功能开发中...')
      break
    case 'share':
      shareVisible.value = true
      break
    case 'delete':
      await handleDeleteFile(file)
      break
  }
}

// 处理批量命令
const handleBatchCommand = async (command: string) => {
  const selectedFiles = fileStore.getSelectedFiles()

  switch (command) {
    case 'download':
      // TODO: 实现批量下载
      ElMessage.info('批量下载功能开发中...')
      break
    case 'move':
      // TODO: 实现批量移动
      ElMessage.info('批量移动功能开发中...')
      break
    case 'copy':
      // TODO: 实现批量复制
      ElMessage.info('批量复制功能开发中...')
      break
    case 'delete':
      await handleBatchDelete(selectedFiles)
      break
  }
}

// 处理排序命令
const handleSortCommand = (field: string) => {
  const order = fileStore.sortBy === field && fileStore.sortOrder === 'desc' ? 'asc' : 'desc'
  fileStore.setSorting(field, order)
  loadFileList()
}

// 删除文件
const handleDeleteFile = async (file: FileItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除 "${file.name}" 吗？删除后可在回收站中恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    await deleteFile(file.id)
    ElMessage.success('删除成功')
    loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除文件失败:', error)
    }
  }
}

// 批量删除
const handleBatchDelete = async (files: FileItem[]) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除选中的 ${files.length} 个文件吗？删除后可在回收站中恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    )

    // 并发删除
    await Promise.all(files.map(file => deleteFile(file.id)))
    ElMessage.success('删除成功')
    fileStore.clearSelection()
    loadFileList()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量删除失败:', error)
    }
  }
}

// 显示创建文件夹对话框
const showCreateFolderDialog = () => {
  createFolderVisible.value = true
}

// 处理创建文件夹成功
const handleCreateFolderSuccess = () => {
  loadFileList()
}

// 处理重命名成功
const handleRenameSuccess = () => {
  loadFileList()
}

// 上传相关处理
const beforeUpload = (file: File) => {
  const isLt2G = file.size / 1024 / 1024 / 1024 < 2
  if (!isLt2G) {
    ElMessage.error('上传文件大小不能超过 2GB!')
    return false
  }
  return true
}

const handleUploadSuccess = (response: any) => {
  ElMessage.success('上传成功')
  loadFileList()
  userStore.getStorageInfoAction() // 更新存储空间信息
}

const handleUploadError = (error: any) => {
  console.error('上传失败:', error)
  ElMessage.error('上传失败')
}

const handleUploadProgress = (event: any) => {
  // TODO: 显示上传进度
}

// 分页处理
const handlePageSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadFileList()
}

const handleCurrentPageChange = (page: number) => {
  currentPage.value = page
  loadFileList()
}

// 监听路由变化
watch(
  () => route.query.folder,
  (folderId) => {
    const id = folderId ? Number(folderId) : undefined
    fileStore.setCurrentPath(id)
    currentPage.value = 1
    loadFileList()
  },
  { immediate: true }
)

// 监听搜索
watch(
  () => route.query.search,
  (keyword) => {
    if (keyword) {
      fileStore.setSearchKeyword(keyword as string)
      // TODO: 执行搜索
    } else {
      fileStore.setSearchKeyword('')
      loadFileList()
    }
  },
  { immediate: true }
)

onMounted(() => {
  loadFileList()
})
</script>

<style scoped>
.files-page {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px 20px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

.toolbar-left {
  display: flex;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.view-toggle {
  border-radius: 8px;
}

.file-content {
  flex: 1;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
}

/* 列表视图样式 */
.list-view {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.list-header {
  display: flex;
  align-items: center;
  height: 48px;
  padding: 0 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
  font-weight: 600;
  color: #606266;
}

.list-body {
  flex: 1;
  overflow-y: auto;
}

.file-item {
  display: flex;
  align-items: center;
  height: 56px;
  padding: 0 20px;
  border-bottom: 1px solid #f5f7fa;
  cursor: pointer;
  transition: background 0.3s ease;
}

.file-item:hover {
  background: #f8f9fa;
}

.file-item.selected {
  background: #ecf5ff;
}

.header-cell,
.item-cell {
  display: flex;
  align-items: center;
}

.checkbox-cell {
  width: 40px;
}

.name-cell {
  flex: 1;
  min-width: 200px;
}

.size-cell {
  width: 100px;
}

.time-cell {
  width: 150px;
}

.action-cell {
  width: 60px;
  justify-content: center;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 20px;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

/* 网格视图样式 */
.grid-view {
  padding: 20px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
  gap: 16px;
  overflow-y: auto;
}

.grid-item {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 12px;
  padding: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.grid-item:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.grid-item.selected {
  border-color: #409eff;
  background: #ecf5ff;
}

.grid-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.grid-item-content {
  text-align: center;
}

.file-preview {
  margin-bottom: 12px;
}

.preview-icon {
  font-size: 48px;
}

.file-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.file-size,
.file-time {
  font-size: 12px;
  color: #909399;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #909399;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  font-size: 16px;
  margin: 0 0 8px 0;
}

.empty-desc {
  font-size: 14px;
  margin: 0;
  opacity: 0.8;
}

/* 分页样式 */
.pagination {
  display: flex;
  justify-content: center;
  padding: 20px;
  background: #fff;
  border-radius: 12px;
  margin-top: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .grid-view {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
    padding: 16px;
  }

  .list-header {
    display: none;
  }

  .file-item {
    flex-wrap: wrap;
    height: auto;
    padding: 12px 16px;
  }

  .size-cell,
  .time-cell {
    display: none;
  }
}
</style>
