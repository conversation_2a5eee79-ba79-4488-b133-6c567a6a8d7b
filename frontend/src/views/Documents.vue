<template>
  <div class="documents-page">
    <div class="page-header">
      <h2 class="page-title">文档</h2>
      <p class="page-description">查看您的所有文档文件</p>
    </div>
    
    <div class="coming-soon">
      <el-icon class="coming-soon-icon"><Document /></el-icon>
      <h3>功能开发中</h3>
      <p>文档分类功能正在开发中，敬请期待...</p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { Document } from '@element-plus/icons-vue'
</script>

<style scoped>
.documents-page {
  height: 100%;
}

.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 14px;
  color: #606266;
  margin: 0;
}

.coming-soon {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 400px;
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  text-align: center;
}

.coming-soon-icon {
  font-size: 64px;
  color: #2196f3;
  margin-bottom: 16px;
}

.coming-soon h3 {
  font-size: 18px;
  color: #303133;
  margin: 0 0 8px 0;
}

.coming-soon p {
  font-size: 14px;
  color: #606266;
  margin: 0;
}
</style>
