<template>
  <div class="share-page">
    <div class="share-container">
      <div class="share-header">
        <div class="logo">
          <el-icon class="logo-icon"><CloudIcon /></el-icon>
          <h1 class="logo-text">江江网盘</h1>
        </div>
        <p class="share-title">文件分享</p>
      </div>
      
      <div v-if="loading" class="loading-state">
        <el-icon class="loading-icon"><Loading /></el-icon>
        <p>加载中...</p>
      </div>
      
      <div v-else-if="error" class="error-state">
        <el-icon class="error-icon"><Warning /></el-icon>
        <h3>{{ error }}</h3>
        <p>请检查分享链接是否正确</p>
      </div>
      
      <div v-else class="share-content">
        <div class="file-info">
          <div class="file-preview">
            <el-icon class="file-icon" :style="{ color: getFileTypeColor(shareInfo?.file?.name || '', shareInfo?.file?.type === 'folder') }">
              <component :is="getFileIconComponent(shareInfo?.file?.name || '', shareInfo?.file?.type === 'folder')" />
            </el-icon>
          </div>
          
          <div class="file-details">
            <h2 class="file-name">{{ shareInfo?.file?.name }}</h2>
            <div class="file-meta">
              <span class="file-size">
                {{ shareInfo?.file?.type === 'folder' ? '文件夹' : formatFileSize(shareInfo?.file?.size || 0) }}
              </span>
              <span class="share-time">
                分享于 {{ formatTime(shareInfo?.created_at || '') }}
              </span>
            </div>
          </div>
        </div>
        
        <div v-if="!accessed && shareInfo?.has_password" class="password-form">
          <el-form @submit.prevent="handleAccess">
            <el-form-item>
              <el-input
                v-model="password"
                type="password"
                placeholder="请输入提取密码"
                size="large"
                :prefix-icon="Lock"
                show-password
                @keyup.enter="handleAccess"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="large"
                :loading="accessing"
                @click="handleAccess"
                class="access-btn"
              >
                {{ accessing ? '验证中...' : '提取文件' }}
              </el-button>
            </el-form-item>
          </el-form>
        </div>
        
        <div v-else class="download-section">
          <el-button
            type="primary"
            size="large"
            :icon="Download"
            @click="handleDownload"
            class="download-btn"
          >
            下载文件
          </el-button>
          
          <div class="share-stats">
            <span>浏览 {{ shareInfo?.view_count }} 次</span>
            <span>下载 {{ shareInfo?.download_count }} 次</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Cloud as CloudIcon,
  Loading,
  Warning,
  Lock,
  Download,
  Folder,
  Document,
} from '@element-plus/icons-vue'
import { getShareByCode, accessShare, downloadShareFile } from '@/api/share'
import { formatFileSize, formatTime, getFileIcon, getFileTypeColor } from '@/utils'
import type { Share } from '@/types'

const route = useRoute()

const loading = ref(true)
const error = ref('')
const shareInfo = ref<Share | null>(null)
const accessed = ref(false)
const password = ref('')
const accessing = ref(false)

// 获取文件图标组件
const getFileIconComponent = (fileName: string, isFolder: boolean) => {
  const iconName = getFileIcon(fileName, isFolder)
  const iconMap: Record<string, any> = {
    folder: Folder,
    document: Document,
  }
  return iconMap[iconName] || Document
}

// 加载分享信息
const loadShareInfo = async () => {
  try {
    loading.value = true
    const code = route.params.code as string
    
    if (!code) {
      error.value = '分享链接无效'
      return
    }
    
    const response = await getShareByCode(code)
    shareInfo.value = response
    
    // 如果没有密码，直接标记为已访问
    if (!response.has_password) {
      accessed.value = true
    }
  } catch (err: any) {
    error.value = err.message || '获取分享信息失败'
  } finally {
    loading.value = false
  }
}

// 处理密码验证
const handleAccess = async () => {
  if (!shareInfo.value) return
  
  try {
    accessing.value = true
    const code = route.params.code as string
    
    await accessShare(code, { password: password.value })
    accessed.value = true
    ElMessage.success('验证成功')
  } catch (err: any) {
    ElMessage.error(err.message || '密码错误')
  } finally {
    accessing.value = false
  }
}

// 处理下载
const handleDownload = () => {
  if (!shareInfo.value) return
  
  const code = route.params.code as string
  const downloadUrl = downloadShareFile(code, password.value)
  
  window.open(downloadUrl, '_blank')
}

onMounted(() => {
  loadShareInfo()
})
</script>

<style scoped>
.share-page {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.share-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 20px;
  padding: 40px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.share-header {
  text-align: center;
  margin-bottom: 32px;
}

.logo {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 36px;
  color: #409eff;
}

.logo-text {
  font-size: 28px;
  font-weight: 700;
  color: #303133;
  margin: 0;
}

.share-title {
  font-size: 16px;
  color: #606266;
  margin: 0;
}

.loading-state,
.error-state {
  text-align: center;
  padding: 40px 0;
}

.loading-icon,
.error-icon {
  font-size: 48px;
  margin-bottom: 16px;
}

.loading-icon {
  color: #409eff;
  animation: spin 1s linear infinite;
}

.error-icon {
  color: #f56c6c;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.file-info {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
}

.file-preview {
  flex-shrink: 0;
}

.file-icon {
  font-size: 48px;
}

.file-details {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
  margin: 0 0 8px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-meta {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.file-size,
.share-time {
  font-size: 14px;
  color: #909399;
}

.password-form {
  margin-bottom: 24px;
}

.access-btn,
.download-btn {
  width: 100%;
  height: 48px;
  font-size: 16px;
  font-weight: 600;
  border-radius: 12px;
  background: linear-gradient(135deg, #409eff 0%, #5a67d8 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
  transition: all 0.3s ease;
}

.access-btn:hover,
.download-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(64, 158, 255, 0.4);
}

.download-section {
  text-align: center;
}

.share-stats {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 16px;
  font-size: 14px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .share-container {
    padding: 32px 24px;
  }
  
  .file-info {
    flex-direction: column;
    text-align: center;
  }
  
  .share-stats {
    flex-direction: column;
    gap: 8px;
  }
}
</style>
