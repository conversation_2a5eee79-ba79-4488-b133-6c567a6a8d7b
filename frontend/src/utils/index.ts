import Cookies from 'js-cookie'

// Token相关
export const TOKEN_KEY = 'jiangjiang_token'

export const getToken = (): string | undefined => {
  return Cookies.get(TOKEN_KEY)
}

export const setToken = (token: string): void => {
  Cookies.set(TOKEN_KEY, token, { expires: 7 }) // 7天过期
}

export const removeToken = (): void => {
  Cookies.remove(TOKEN_KEY)
}

// 文件大小格式化
export const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 时间格式化
export const formatTime = (time: string): string => {
  const date = new Date(time)
  const now = new Date()
  const diff = now.getTime() - date.getTime()
  
  const minute = 1000 * 60
  const hour = minute * 60
  const day = hour * 24
  const week = day * 7
  const month = day * 30
  const year = day * 365
  
  if (diff < minute) {
    return '刚刚'
  } else if (diff < hour) {
    return Math.floor(diff / minute) + '分钟前'
  } else if (diff < day) {
    return Math.floor(diff / hour) + '小时前'
  } else if (diff < week) {
    return Math.floor(diff / day) + '天前'
  } else if (diff < month) {
    return Math.floor(diff / week) + '周前'
  } else if (diff < year) {
    return Math.floor(diff / month) + '个月前'
  } else {
    return Math.floor(diff / year) + '年前'
  }
}

// 获取文件图标
export const getFileIcon = (fileName: string, isFolder: boolean): string => {
  if (isFolder) {
    return 'folder'
  }
  
  const ext = fileName.split('.').pop()?.toLowerCase() || ''
  
  const iconMap: Record<string, string> = {
    // 图片
    jpg: 'picture',
    jpeg: 'picture',
    png: 'picture',
    gif: 'picture',
    bmp: 'picture',
    webp: 'picture',
    svg: 'picture',
    
    // 视频
    mp4: 'video-camera',
    avi: 'video-camera',
    mov: 'video-camera',
    wmv: 'video-camera',
    flv: 'video-camera',
    webm: 'video-camera',
    mkv: 'video-camera',
    
    // 音频
    mp3: 'headphones',
    wav: 'headphones',
    flac: 'headphones',
    aac: 'headphones',
    ogg: 'headphones',
    wma: 'headphones',
    
    // 文档
    pdf: 'document',
    doc: 'document',
    docx: 'document',
    xls: 'document',
    xlsx: 'document',
    ppt: 'document',
    pptx: 'document',
    txt: 'document',
    rtf: 'document',
    
    // 压缩文件
    zip: 'folder-zip',
    rar: 'folder-zip',
    '7z': 'folder-zip',
    tar: 'folder-zip',
    gz: 'folder-zip',
    
    // 代码文件
    html: 'document',
    css: 'document',
    js: 'document',
    json: 'document',
    xml: 'document',
    go: 'document',
    py: 'document',
    java: 'document',
    cpp: 'document',
    c: 'document',
    php: 'document',
  }
  
  return iconMap[ext] || 'document'
}

// 获取文件类型颜色
export const getFileTypeColor = (fileName: string, isFolder: boolean): string => {
  if (isFolder) {
    return '#409EFF'
  }
  
  const ext = fileName.split('.').pop()?.toLowerCase() || ''
  
  const colorMap: Record<string, string> = {
    // 图片 - 绿色
    jpg: '#67C23A',
    jpeg: '#67C23A',
    png: '#67C23A',
    gif: '#67C23A',
    bmp: '#67C23A',
    webp: '#67C23A',
    svg: '#67C23A',
    
    // 视频 - 紫色
    mp4: '#9C27B0',
    avi: '#9C27B0',
    mov: '#9C27B0',
    wmv: '#9C27B0',
    flv: '#9C27B0',
    webm: '#9C27B0',
    mkv: '#9C27B0',
    
    // 音频 - 橙色
    mp3: '#FF9800',
    wav: '#FF9800',
    flac: '#FF9800',
    aac: '#FF9800',
    ogg: '#FF9800',
    wma: '#FF9800',
    
    // 文档 - 蓝色
    pdf: '#2196F3',
    doc: '#2196F3',
    docx: '#2196F3',
    xls: '#4CAF50',
    xlsx: '#4CAF50',
    ppt: '#FF5722',
    pptx: '#FF5722',
    txt: '#607D8B',
    rtf: '#607D8B',
    
    // 压缩文件 - 黄色
    zip: '#FFC107',
    rar: '#FFC107',
    '7z': '#FFC107',
    tar: '#FFC107',
    gz: '#FFC107',
    
    // 代码文件 - 深蓝色
    html: '#E91E63',
    css: '#3F51B5',
    js: '#FFEB3B',
    json: '#795548',
    xml: '#009688',
    go: '#00BCD4',
    py: '#8BC34A',
    java: '#FF5722',
    cpp: '#9C27B0',
    c: '#607D8B',
    php: '#673AB7',
  }
  
  return colorMap[ext] || '#909399'
}

// 复制到剪贴板
export const copyToClipboard = async (text: string): Promise<boolean> => {
  try {
    await navigator.clipboard.writeText(text)
    return true
  } catch (err) {
    // 降级方案
    const textArea = document.createElement('textarea')
    textArea.value = text
    document.body.appendChild(textArea)
    textArea.focus()
    textArea.select()
    try {
      document.execCommand('copy')
      document.body.removeChild(textArea)
      return true
    } catch (err) {
      document.body.removeChild(textArea)
      return false
    }
  }
}

// 下载文件
export const downloadFile = (url: string, filename: string): void => {
  const link = document.createElement('a')
  link.href = url
  link.download = filename
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 防抖函数
export const debounce = <T extends (...args: any[]) => any>(
  func: T,
  wait: number
): ((...args: Parameters<T>) => void) => {
  let timeout: NodeJS.Timeout | null = null
  
  return (...args: Parameters<T>) => {
    if (timeout) {
      clearTimeout(timeout)
    }
    timeout = setTimeout(() => func(...args), wait)
  }
}
