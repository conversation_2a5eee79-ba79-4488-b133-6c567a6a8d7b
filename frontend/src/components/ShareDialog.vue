<template>
  <el-dialog
    v-model="visible"
    title="分享文件"
    width="500px"
    :before-close="handleClose"
    class="share-dialog"
  >
    <div v-if="!shareInfo" class="share-form">
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-form-item label="分享密码" prop="password">
          <el-input
            v-model="form.password"
            placeholder="留空表示无密码（可选）"
            clearable
            maxlength="20"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item label="有效期" prop="expire_days">
          <el-select
            v-model="form.expire_days"
            placeholder="请选择有效期"
            style="width: 100%"
          >
            <el-option label="永久有效" :value="0" />
            <el-option label="1天" :value="1" />
            <el-option label="7天" :value="7" />
            <el-option label="30天" :value="30" />
          </el-select>
        </el-form-item>
      </el-form>
    </div>
    
    <div v-else class="share-result">
      <div class="result-header">
        <el-icon class="success-icon"><Check /></el-icon>
        <h3>分享创建成功</h3>
      </div>
      
      <div class="share-info">
        <div class="info-item">
          <label>分享链接：</label>
          <div class="link-container">
            <el-input
              :model-value="shareLink"
              readonly
              class="share-link"
            />
            <el-button
              type="primary"
              @click="copyLink"
            >
              复制链接
            </el-button>
          </div>
        </div>
        
        <div v-if="shareInfo.has_password" class="info-item">
          <label>提取密码：</label>
          <div class="password-container">
            <span class="password-text">{{ form.password }}</span>
            <el-button
              text
              @click="copyPassword"
            >
              复制密码
            </el-button>
          </div>
        </div>
        
        <div class="info-item">
          <label>有效期：</label>
          <span>{{ getExpireText() }}</span>
        </div>
      </div>
      
      <div class="quick-share">
        <p class="quick-title">快速分享</p>
        <el-button
          type="primary"
          @click="copyShareText"
        >
          复制分享文本
        </el-button>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">
          {{ shareInfo ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="!shareInfo"
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '创建中...' : '创建分享' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import { createShare } from '@/api/share'
import { copyToClipboard } from '@/utils'
import type { FileItem, CreateShareForm, Share } from '@/types'

interface Props {
  modelValue: boolean
  file?: FileItem | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const loading = ref(false)
const shareInfo = ref<Share | null>(null)

// 表单数据
const form = reactive<CreateShareForm>({
  file_id: 0,
  password: '',
  expire_days: 7,
})

// 验证规则
const rules: FormRules = {
  password: [
    { max: 20, message: '密码长度不能超过 20 个字符', trigger: 'blur' },
  ],
  expire_days: [
    { required: true, message: '请选择有效期', trigger: 'change' },
  ],
}

// 控制对话框显示
const visible = ref(false)

// 分享链接
const shareLink = computed(() => {
  if (!shareInfo.value) return ''
  return `${window.location.origin}/share/${shareInfo.value.share_code}`
})

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val && props.file) {
      form.file_id = props.file.id
      shareInfo.value = null
    }
  },
  { immediate: true }
)

// 监听 visible 变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 获取有效期文本
const getExpireText = () => {
  if (!shareInfo.value) return ''
  
  if (!shareInfo.value.expire_time) {
    return '永久有效'
  }
  
  const expireDate = new Date(shareInfo.value.expire_time)
  return expireDate.toLocaleString()
}

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || !props.file) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    const response = await createShare(form)
    shareInfo.value = response
    
    ElMessage.success('分享创建成功')
  } catch (error) {
    console.error('创建分享失败:', error)
  } finally {
    loading.value = false
  }
}

// 复制链接
const copyLink = async () => {
  const success = await copyToClipboard(shareLink.value)
  if (success) {
    ElMessage.success('链接已复制到剪贴板')
  } else {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 复制密码
const copyPassword = async () => {
  const success = await copyToClipboard(form.password)
  if (success) {
    ElMessage.success('密码已复制到剪贴板')
  } else {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 复制分享文本
const copyShareText = async () => {
  let text = `分享文件：${props.file?.name}\n链接：${shareLink.value}`
  
  if (shareInfo.value?.has_password) {
    text += `\n提取密码：${form.password}`
  }
  
  if (shareInfo.value?.expire_time) {
    text += `\n有效期至：${getExpireText()}`
  }
  
  const success = await copyToClipboard(text)
  if (success) {
    ElMessage.success('分享文本已复制到剪贴板')
  } else {
    ElMessage.error('复制失败，请手动复制')
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
  shareInfo.value = null
  form.password = ''
  form.expire_days = 7
  formRef.value?.resetFields()
}
</script>

<style scoped>
.share-dialog :deep(.el-dialog__header) {
  padding: 20px 20px 0;
}

.share-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.share-dialog :deep(.el-dialog__footer) {
  padding: 0 20px 20px;
}

.share-form {
  margin-bottom: 20px;
}

.share-result {
  text-align: center;
}

.result-header {
  margin-bottom: 24px;
}

.success-icon {
  font-size: 48px;
  color: #67c23a;
  margin-bottom: 12px;
}

.result-header h3 {
  margin: 0;
  font-size: 18px;
  color: #303133;
}

.share-info {
  text-align: left;
  margin-bottom: 24px;
}

.info-item {
  margin-bottom: 16px;
}

.info-item label {
  display: block;
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.link-container {
  display: flex;
  gap: 12px;
}

.share-link {
  flex: 1;
}

.password-container {
  display: flex;
  align-items: center;
  gap: 12px;
}

.password-text {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
  font-family: monospace;
  background: #f0f9ff;
  padding: 4px 8px;
  border-radius: 4px;
}

.quick-share {
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 20px;
}

.quick-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
