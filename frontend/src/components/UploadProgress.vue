<template>
  <div class="upload-progress-container">
    <div class="upload-progress-card">
      <div class="progress-header">
        <div class="header-left">
          <el-icon class="upload-icon"><Upload /></el-icon>
          <span class="title">文件上传</span>
        </div>
        <div class="header-right">
          <el-button
            :icon="fileStore.isUploading ? Pause : Play"
            text
            size="small"
            @click="toggleUpload"
          />
          <el-button
            :icon="Minus"
            text
            size="small"
            @click="minimizePanel"
          />
          <el-button
            :icon="Close"
            text
            size="small"
            @click="closePanel"
          />
        </div>
      </div>
      
      <div class="progress-content">
        <!-- 总体进度 -->
        <div class="overall-progress">
          <div class="progress-info">
            <span class="progress-text">
              {{ uploadingCount }} / {{ totalCount }} 个文件
            </span>
            <span class="progress-percentage">{{ fileStore.uploadProgress }}%</span>
          </div>
          <el-progress
            :percentage="fileStore.uploadProgress"
            :stroke-width="6"
            :show-text="false"
            :color="progressColor"
          />
        </div>
        
        <!-- 文件列表 -->
        <div class="file-list">
          <div
            v-for="(file, index) in fileStore.uploadQueue"
            :key="index"
            class="upload-item"
            :class="getItemClass(file.status)"
          >
            <div class="item-left">
              <el-icon class="file-icon">
                <component :is="getStatusIcon(file.status)" />
              </el-icon>
              <div class="file-info">
                <div class="file-name" :title="file.file.name">{{ file.file.name }}</div>
                <div class="file-size">{{ formatFileSize(file.file.size) }}</div>
              </div>
            </div>
            
            <div class="item-right">
              <div v-if="file.status === 'uploading'" class="progress-wrapper">
                <el-progress
                  :percentage="file.progress"
                  :stroke-width="4"
                  :show-text="false"
                  :color="progressColor"
                />
                <span class="progress-text">{{ file.progress }}%</span>
              </div>
              
              <div v-else-if="file.status === 'success'" class="status-success">
                <el-icon><Check /></el-icon>
                <span>完成</span>
              </div>
              
              <div v-else-if="file.status === 'error'" class="status-error">
                <el-icon><Close /></el-icon>
                <span>失败</span>
                <el-button
                  text
                  size="small"
                  @click="retryUpload(index)"
                >
                  重试
                </el-button>
              </div>
              
              <div v-else class="status-waiting">
                <span>等待中</span>
              </div>
              
              <el-button
                :icon="Close"
                text
                size="small"
                @click="removeUploadItem(index)"
              />
            </div>
          </div>
        </div>
      </div>
      
      <div class="progress-footer">
        <el-button size="small" @click="clearCompleted">
          清除已完成
        </el-button>
        <el-button
          v-if="hasErrors"
          type="primary"
          size="small"
          @click="retryAll"
        >
          重试全部
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  Upload,
  Pause,
  Play,
  Minus,
  Close,
  Check,
  Document,
  Warning,
  Clock,
} from '@element-plus/icons-vue'
import { useFileStore } from '@/store/file'
import { formatFileSize } from '@/utils'

const fileStore = useFileStore()

// 计算属性
const totalCount = computed(() => fileStore.uploadQueue.length)
const uploadingCount = computed(() => 
  fileStore.uploadQueue.filter(file => file.status === 'uploading' || file.status === 'success').length
)
const hasErrors = computed(() => 
  fileStore.uploadQueue.some(file => file.status === 'error')
)

const progressColor = computed(() => {
  const progress = fileStore.uploadProgress
  if (progress < 50) return '#67c23a'
  if (progress < 80) return '#e6a23c'
  return '#f56c6c'
})

// 获取状态图标
const getStatusIcon = (status: string) => {
  switch (status) {
    case 'uploading':
      return Upload
    case 'success':
      return Check
    case 'error':
      return Warning
    case 'waiting':
    default:
      return Clock
  }
}

// 获取项目样式类
const getItemClass = (status: string) => {
  return {
    'item-uploading': status === 'uploading',
    'item-success': status === 'success',
    'item-error': status === 'error',
    'item-waiting': status === 'waiting',
  }
}

// 切换上传状态
const toggleUpload = () => {
  // TODO: 实现暂停/继续上传
}

// 最小化面板
const minimizePanel = () => {
  // TODO: 实现最小化
}

// 关闭面板
const closePanel = () => {
  fileStore.clearUploadQueue()
}

// 重试上传
const retryUpload = (index: number) => {
  fileStore.updateUploadStatus(index, 'waiting')
  // TODO: 重新开始上传
}

// 重试全部
const retryAll = () => {
  fileStore.uploadQueue.forEach((file, index) => {
    if (file.status === 'error') {
      fileStore.updateUploadStatus(index, 'waiting')
    }
  })
  // TODO: 重新开始所有失败的上传
}

// 移除上传项
const removeUploadItem = (index: number) => {
  fileStore.removeUploadTask(index)
}

// 清除已完成
const clearCompleted = () => {
  fileStore.clearUploadQueue()
}
</script>

<style scoped>
.upload-progress-container {
  position: fixed;
  bottom: 20px;
  right: 20px;
  z-index: 1000;
  width: 400px;
  max-height: 500px;
}

.upload-progress-card {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid #e4e7ed;
  overflow: hidden;
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.upload-icon {
  font-size: 18px;
  color: #409eff;
}

.title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  display: flex;
  gap: 4px;
}

.progress-content {
  padding: 16px 20px;
  max-height: 300px;
  overflow-y: auto;
}

.overall-progress {
  margin-bottom: 16px;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.progress-text {
  font-size: 14px;
  color: #606266;
}

.progress-percentage {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.file-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.upload-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.upload-item.item-uploading {
  background: #ecf5ff;
  border: 1px solid #b3d8ff;
}

.upload-item.item-success {
  background: #f0f9ff;
  border: 1px solid #b3e5fc;
}

.upload-item.item-error {
  background: #fef0f0;
  border: 1px solid #fbc4c4;
}

.item-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.file-icon {
  font-size: 16px;
  color: #409eff;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.file-size {
  font-size: 12px;
  color: #909399;
}

.item-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.progress-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 80px;
}

.progress-wrapper .progress-text {
  font-size: 12px;
  color: #606266;
  white-space: nowrap;
}

.status-success,
.status-error,
.status-waiting {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
}

.status-success {
  color: #67c23a;
}

.status-error {
  color: #f56c6c;
}

.status-waiting {
  color: #909399;
}

.progress-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 20px;
  background: #f8f9fa;
  border-top: 1px solid #e4e7ed;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .upload-progress-container {
    width: calc(100vw - 40px);
    right: 20px;
    left: 20px;
  }
}
</style>
