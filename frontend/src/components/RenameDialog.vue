<template>
  <el-dialog
    v-model="visible"
    title="重命名"
    width="400px"
    :before-close="handleClose"
    class="rename-dialog"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="0"
      @submit.prevent="handleSubmit"
    >
      <el-form-item prop="name">
        <el-input
          v-model="form.name"
          placeholder="请输入新名称"
          size="large"
          :prefix-icon="Edit"
          clearable
          @keyup.enter="handleSubmit"
          ref="inputRef"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button
          type="primary"
          :loading="loading"
          @click="handleSubmit"
        >
          {{ loading ? '重命名中...' : '确定' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Edit } from '@element-plus/icons-vue'
import { renameFile } from '@/api/file'
import type { FileItem, RenameForm } from '@/types'

interface Props {
  modelValue: boolean
  file?: FileItem | null
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const formRef = ref<FormInstance>()
const inputRef = ref()
const loading = ref(false)

// 表单数据
const form = reactive<RenameForm>({
  name: '',
})

// 验证规则
const rules: FormRules = {
  name: [
    { required: true, message: '请输入名称', trigger: 'blur' },
    { min: 1, max: 255, message: '名称长度在 1 到 255 个字符', trigger: 'blur' },
    { 
      pattern: /^[^\\/:*?"<>|]+$/, 
      message: '名称不能包含 \\ / : * ? " < > | 等字符', 
      trigger: 'blur' 
    },
  ],
}

// 控制对话框显示
const visible = ref(false)

// 监听 modelValue 变化
watch(
  () => props.modelValue,
  (val) => {
    visible.value = val
    if (val && props.file) {
      form.name = props.file.name
      nextTick(() => {
        inputRef.value?.focus()
        // 选中文件名（不包括扩展名）
        const input = inputRef.value?.input
        if (input && props.file?.type === 'file') {
          const lastDotIndex = props.file.name.lastIndexOf('.')
          if (lastDotIndex > 0) {
            input.setSelectionRange(0, lastDotIndex)
          } else {
            input.select()
          }
        } else if (input) {
          input.select()
        }
      })
    }
  },
  { immediate: true }
)

// 监听 visible 变化
watch(visible, (val) => {
  emit('update:modelValue', val)
})

// 处理提交
const handleSubmit = async () => {
  if (!formRef.value || !props.file) return
  
  try {
    await formRef.value.validate()
    
    // 检查名称是否有变化
    if (form.name === props.file.name) {
      handleClose()
      return
    }
    
    loading.value = true
    
    await renameFile(props.file.id, form)
    
    ElMessage.success('重命名成功')
    emit('success')
    handleClose()
  } catch (error) {
    console.error('重命名失败:', error)
  } finally {
    loading.value = false
  }
}

// 处理关闭
const handleClose = () => {
  visible.value = false
  form.name = ''
  formRef.value?.resetFields()
}
</script>

<style scoped>
.rename-dialog :deep(.el-dialog__header) {
  padding: 20px 20px 0;
}

.rename-dialog :deep(.el-dialog__body) {
  padding: 20px;
}

.rename-dialog :deep(.el-dialog__footer) {
  padding: 0 20px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

.rename-dialog :deep(.el-input__wrapper) {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.rename-dialog :deep(.el-input__wrapper:hover) {
  border-color: #409eff;
}

.rename-dialog :deep(.el-input__wrapper.is-focus) {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}
</style>
