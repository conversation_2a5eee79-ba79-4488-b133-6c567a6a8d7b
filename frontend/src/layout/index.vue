<template>
  <div class="layout">
    <!-- 侧边栏 -->
    <aside class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <el-icon class="logo-icon"><CloudIcon /></el-icon>
          <span v-show="!sidebarCollapsed" class="logo-text">江江网盘</span>
        </div>
        <el-button
          class="collapse-btn"
          :icon="sidebarCollapsed ? Expand : Fold"
          @click="toggleSidebar"
          text
        />
      </div>
      
      <div class="sidebar-content">
        <!-- 存储空间信息 -->
        <div v-show="!sidebarCollapsed" class="storage-info">
          <div class="storage-header">
            <span class="storage-title">存储空间</span>
            <span class="storage-usage">{{ storageInfo?.used_space_str }} / {{ storageInfo?.total_space_str }}</span>
          </div>
          <el-progress
            :percentage="userStore.usagePercentage"
            :stroke-width="6"
            :show-text="false"
            :color="getProgressColor(userStore.usagePercentage)"
          />
        </div>
        
        <!-- 导航菜单 -->
        <nav class="nav-menu">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ active: $route.path === item.path }"
          >
            <el-icon class="nav-icon">
              <component :is="item.icon" />
            </el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">{{ item.title }}</span>
          </router-link>
        </nav>
      </div>
    </aside>
    
    <!-- 主内容区 -->
    <main class="main-content">
      <!-- 顶部栏 -->
      <header class="header">
        <div class="header-left">
          <!-- 面包屑导航 -->
          <el-breadcrumb separator="/" class="breadcrumb">
            <el-breadcrumb-item
              v-for="(item, index) in fileStore.breadcrumbs"
              :key="index"
              :to="item.path"
            >
              {{ item.name }}
            </el-breadcrumb-item>
          </el-breadcrumb>
        </div>
        
        <div class="header-right">
          <!-- 搜索框 -->
          <div class="search-box">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索文件..."
              :prefix-icon="Search"
              clearable
              @keyup.enter="handleSearch"
              @clear="handleSearchClear"
            />
          </div>
          
          <!-- 用户菜单 -->
          <el-dropdown class="user-dropdown" @command="handleUserCommand">
            <div class="user-info">
              <el-avatar :src="userStore.avatar" :size="32">
                <el-icon><User /></el-icon>
              </el-avatar>
              <span class="username">{{ userStore.nickname }}</span>
              <el-icon class="dropdown-icon"><ArrowDown /></el-icon>
            </div>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="profile">
                  <el-icon><User /></el-icon>
                  个人资料
                </el-dropdown-item>
                <el-dropdown-item command="settings">
                  <el-icon><Setting /></el-icon>
                  设置
                </el-dropdown-item>
                <el-dropdown-item divided command="logout">
                  <el-icon><SwitchButton /></el-icon>
                  退出登录
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <div class="page-content">
        <router-view />
      </div>
    </main>
    
    <!-- 上传进度浮窗 -->
    <UploadProgress v-if="fileStore.isUploading" />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessageBox } from 'element-plus'
import {
  Fold,
  Expand,
  Search,
  User,
  ArrowDown,
  Setting,
  SwitchButton,
  Folder,
  Clock,
  Picture,
  VideoCamera,
  Document,
  Share,
  Delete,
  Cloud as CloudIcon,
} from '@element-plus/icons-vue'
import { useUserStore } from '@/store/user'
import { useFileStore } from '@/store/file'
import UploadProgress from '@/components/UploadProgress.vue'

const router = useRouter()
const userStore = useUserStore()
const fileStore = useFileStore()

// 侧边栏状态
const sidebarCollapsed = ref(false)
const searchKeyword = ref('')

// 存储信息
const storageInfo = computed(() => userStore.storageInfo)

// 菜单项
const menuItems = [
  { path: '/files', title: '全部文件', icon: Folder },
  { path: '/recent', title: '最近使用', icon: Clock },
  { path: '/images', title: '图片', icon: Picture },
  { path: '/videos', title: '视频', icon: VideoCamera },
  { path: '/documents', title: '文档', icon: Document },
  { path: '/shares', title: '我的分享', icon: Share },
  { path: '/trash', title: '回收站', icon: Delete },
]

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 60) return '#67c23a'
  if (percentage < 80) return '#e6a23c'
  return '#f56c6c'
}

// 处理搜索
const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    router.push({
      path: '/files',
      query: { search: searchKeyword.value.trim() }
    })
  }
}

// 清除搜索
const handleSearchClear = () => {
  if (router.currentRoute.value.query.search) {
    router.push({ path: '/files' })
  }
}

// 处理用户菜单命令
const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      // 打开个人资料对话框
      break
    case 'settings':
      router.push('/settings')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm('确定要退出登录吗？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        })
        userStore.logout()
        router.push('/login')
      } catch {
        // 用户取消
      }
      break
  }
}

onMounted(() => {
  // 初始化用户信息
  userStore.init()
})
</script>

<style scoped>
.layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

.sidebar {
  width: 240px;
  background: #fff;
  border-right: 1px solid #e4e7ed;
  transition: width 0.3s ease;
  display: flex;
  flex-direction: column;
}

.sidebar.collapsed {
  width: 64px;
}

.sidebar-header {
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-icon {
  font-size: 24px;
  color: #409eff;
}

.logo-text {
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.collapse-btn {
  padding: 8px;
}

.sidebar-content {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.storage-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8f9fa;
  border-radius: 8px;
}

.storage-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.storage-title {
  font-size: 14px;
  color: #606266;
}

.storage-usage {
  font-size: 12px;
  color: #909399;
}

.nav-menu {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border-radius: 8px;
  color: #606266;
  text-decoration: none;
  transition: all 0.3s ease;
}

.nav-item:hover {
  background: #f5f7fa;
  color: #409eff;
}

.nav-item.active {
  background: #ecf5ff;
  color: #409eff;
}

.nav-icon {
  font-size: 18px;
  min-width: 18px;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.header {
  height: 60px;
  background: #fff;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.header-left {
  flex: 1;
}

.breadcrumb {
  font-size: 14px;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-box {
  width: 300px;
}

.user-dropdown {
  cursor: pointer;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 6px;
  transition: background 0.3s ease;
}

.user-info:hover {
  background: #f5f7fa;
}

.username {
  font-size: 14px;
  color: #303133;
}

.dropdown-icon {
  font-size: 12px;
  color: #909399;
}

.page-content {
  flex: 1;
  overflow: auto;
  padding: 24px;
}
</style>
