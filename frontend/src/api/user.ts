import { request } from '@/utils/request'
import type { User, LoginForm, RegisterForm, StorageInfo } from '@/types'

// 用户注册
export const register = (data: RegisterForm) => {
  return request.post<User>('/auth/register', data)
}

// 用户登录
export const login = (data: LoginForm) => {
  return request.post<{ token: string; user: User }>('/auth/login', data)
}

// 获取用户信息
export const getUserProfile = () => {
  return request.get<User>('/user/profile')
}

// 更新用户信息
export const updateUserProfile = (data: { nickname?: string; avatar_url?: string }) => {
  return request.put<User>('/user/profile', data)
}

// 修改密码
export const changePassword = (data: { old_password: string; new_password: string }) => {
  return request.post('/user/change-password', data)
}

// 获取存储空间信息
export const getStorageInfo = () => {
  return request.get<StorageInfo>('/user/storage')
}
