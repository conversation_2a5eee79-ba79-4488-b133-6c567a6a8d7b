import { request } from '@/utils/request'
import type { 
  Share, 
  CreateShareForm, 
  ShareAccessForm,
  PageResponse,
  FileItem
} from '@/types'

// 创建分享
export const createShare = (data: CreateShareForm) => {
  return request.post<Share>('/user/shares', data)
}

// 获取用户分享列表
export const getUserShares = (params: { page?: number; page_size?: number }) => {
  return request.get<PageResponse<Share>>('/user/shares', { params })
}

// 取消分享
export const cancelShare = (shareId: number) => {
  return request.delete(`/user/shares/${shareId}`)
}

// 更新分享状态
export const updateShareStatus = (shareId: number, isActive: boolean) => {
  return request.put<Share>(`/user/shares/${shareId}/status`, { is_active: isActive })
}

// 根据分享码获取分享信息
export const getShareByCode = (code: string) => {
  return request.get<Share>(`/shares/${code}`)
}

// 访问分享（验证密码）
export const accessShare = (code: string, data: ShareAccessForm) => {
  return request.post<Share>(`/shares/${code}/access`, data)
}

// 下载分享文件
export const downloadShareFile = (code: string, password?: string) => {
  const params = password ? `?password=${password}` : ''
  return `/api/shares/${code}/download${params}`
}
