/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    CreateFolderDialog: typeof import('./src/components/CreateFolderDialog.vue')['default']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElInput: typeof import('element-plus/es')['ElInput']
    RenameDialog: typeof import('./src/components/RenameDialog.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    ShareDialog: typeof import('./src/components/ShareDialog.vue')['default']
    UploadProgress: typeof import('./src/components/UploadProgress.vue')['default']
  }
}
