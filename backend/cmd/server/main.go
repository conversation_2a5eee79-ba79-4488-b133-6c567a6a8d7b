package main

import (
	"fmt"
	"log"
	"os"
	"time"

	"jiangjiang-netdisk/internal/config"
	"jiangjiang-netdisk/internal/handler"
	"jiangjiang-netdisk/internal/middleware"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/repository"
	"jiangjiang-netdisk/internal/service"

	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/schema"
)

func main() {
	// 加载配置
	cfg := config.Load()

	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	// 连接数据库
	db, err := connectDB(cfg)
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// 自动迁移数据库
	err = migrateDB(db)
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	// 确保存储目录存在
	err = os.MkdirAll(cfg.Storage.BasePath, 0755)
	if err != nil {
		log.Fatal("Failed to create storage directory:", err)
	}

	// 初始化仓库层
	userRepo := repository.NewUserRepository(db)
	fileRepo := repository.NewFileRepository(db)
	shareRepo := repository.NewShareRepository(db)

	// 初始化服务层
	userService := service.NewUserService(userRepo, cfg)
	fileService := service.NewFileService(fileRepo, userRepo, cfg, db)
	shareService := service.NewShareService(shareRepo, fileRepo)

	// 初始化处理器层
	userHandler := handler.NewUserHandler(userService)
	fileHandler := handler.NewFileHandler(fileService)
	shareHandler := handler.NewShareHandler(shareService)

	// 创建路由
	router := setupRouter(cfg, userHandler, fileHandler, shareHandler)

	// 启动服务器
	addr := ":" + cfg.Server.Port
	log.Printf("Server starting on %s", addr)
	if err := router.Run(addr); err != nil {
		log.Fatal("Failed to start server:", err)
	}
}

// connectDB 连接数据库
func connectDB(cfg *config.Config) (*gorm.DB, error) {
	// 添加字符编码和时区设置，防止乱码
	dsn := fmt.Sprintf("host=%s port=%s user=%s password=%s dbname=%s sslmode=%s TimeZone=Asia/Shanghai client_encoding=UTF8",
		cfg.Database.Host,
		cfg.Database.Port,
		cfg.Database.User,
		cfg.Database.Password,
		cfg.Database.DBName,
		cfg.Database.SSLMode,
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		// 禁用外键约束检查（可选）
		DisableForeignKeyConstraintWhenMigrating: true,
		// 命名策略
		NamingStrategy: schema.NamingStrategy{
			SingularTable: true, // 使用单数表名
		},
		// 准备语句缓存
		PrepareStmt: true,
	})

	if err != nil {
		return nil, err
	}

	// 获取底层的 sql.DB 对象来配置连接池
	sqlDB, err := db.DB()
	if err != nil {
		return nil, err
	}

	// 设置连接池参数
	sqlDB.SetMaxIdleConns(10)           // 最大空闲连接数
	sqlDB.SetMaxOpenConns(100)          // 最大打开连接数
	sqlDB.SetConnMaxLifetime(time.Hour) // 连接最大生存时间

	// 设置数据库编码，确保中文正常显示
	db.Exec("SET client_encoding = 'UTF8'")
	db.Exec("SET timezone = 'Asia/Shanghai'")

	return db, nil
}

// migrateDB 数据库迁移
func migrateDB(db *gorm.DB) error {
	return db.AutoMigrate(
		&model.User{},
		&model.File{},
		&model.Share{},
	)
}

// setupRouter 设置路由
func setupRouter(cfg *config.Config, userHandler *handler.UserHandler, fileHandler *handler.FileHandler, shareHandler *handler.ShareHandler) *gin.Engine {
	router := gin.New()

	// 添加中间件
	router.Use(middleware.LoggerMiddleware())
	router.Use(middleware.CORSMiddleware())
	router.Use(gin.Recovery())

	// API路由组
	api := router.Group("/api")

	// 认证相关路由（无需认证）
	auth := api.Group("/auth")
	{
		auth.POST("/register", userHandler.Register)
		auth.POST("/login", userHandler.Login)
	}

	// 分享相关路由（无需认证）
	share := api.Group("/shares")
	{
		share.GET("/:code", shareHandler.GetByCode)
		share.POST("/:code/access", shareHandler.Access)
		share.GET("/:code/download", shareHandler.Download)
	}

	// 需要认证的路由
	authMiddleware := middleware.AuthMiddleware(cfg)

	// 用户相关路由
	user := api.Group("/user").Use(authMiddleware)
	{
		user.GET("/profile", userHandler.GetProfile)
		user.PUT("/profile", userHandler.UpdateProfile)
		user.POST("/change-password", userHandler.ChangePassword)
		user.GET("/storage", userHandler.GetStorageInfo)
	}

	// 文件相关路由
	files := api.Group("/files").Use(authMiddleware)
	{
		files.GET("", fileHandler.GetList)
		files.POST("/folder", fileHandler.CreateFolder)
		files.POST("/upload", fileHandler.Upload)
		files.GET("/:id/download", fileHandler.Download)
		files.PUT("/:id/rename", fileHandler.Rename)
		files.POST("/:id/move", fileHandler.Move)
		files.POST("/:id/copy", fileHandler.Copy)
		files.DELETE("/:id", fileHandler.Delete)
		files.GET("/search", fileHandler.Search)
		files.GET("/deleted", fileHandler.GetDeletedFiles)
		files.POST("/:id/restore", fileHandler.Restore)
		files.DELETE("/:id/permanent", fileHandler.PermanentDelete)
	}

	// 用户分享相关路由
	userShares := api.Group("/user/shares").Use(authMiddleware)
	{
		userShares.POST("", shareHandler.Create)
		userShares.GET("", shareHandler.GetUserShares)
		userShares.DELETE("/:id", shareHandler.Cancel)
		userShares.PUT("/:id/status", shareHandler.UpdateStatus)
	}

	return router
}
