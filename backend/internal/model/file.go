package model

import (
	"time"
)

// File 文件模型
type File struct {
	ID        uint       `json:"id" gorm:"primaryKey"`
	UserID    uint       `json:"user_id" gorm:"not null;index"`
	ParentID  *uint      `json:"parent_id" gorm:"index"` // 父文件夹ID，NULL表示根目录
	Name      string     `json:"name" gorm:"size:255;not null"`
	Type      string     `json:"type" gorm:"size:10;not null"` // 'file' or 'folder'
	Size      int64      `json:"size" gorm:"default:0"`
	MimeType  string     `json:"mime_type" gorm:"size:100"`
	FilePath  string     `json:"-" gorm:"size:500"`              // 实际存储路径
	FileHash  string     `json:"file_hash" gorm:"size:64;index"` // 文件MD5哈希
	IsDeleted bool       `json:"is_deleted" gorm:"default:false;index"`
	CreatedAt time.Time  `json:"created_at"`
	UpdatedAt time.Time  `json:"updated_at"`
	DeletedAt *time.Time `json:"deleted_at"`

	// 关联
	User     User   `json:"-" gorm:"foreignKey:UserID"`
	Parent   *File  `json:"-" gorm:"foreignKey:ParentID"`
	Children []File `json:"children,omitempty" gorm:"foreignKey:ParentID"`
}

// FileUploadRequest 文件上传请求
type FileUploadRequest struct {
	Name     string `json:"name" binding:"required"`
	ParentID *uint  `json:"parent_id"`
	Size     int64  `json:"size"`
	Hash     string `json:"hash"`
}

// FileCreateFolderRequest 创建文件夹请求
type FileCreateFolderRequest struct {
	Name     string `json:"name" binding:"required,max=255"`
	ParentID *uint  `json:"parent_id"`
}

// FileRenameRequest 重命名请求
type FileRenameRequest struct {
	Name string `json:"name" binding:"required,max=255"`
}

// FileMoveRequest 移动文件请求
type FileMoveRequest struct {
	ParentID *uint `json:"parent_id"`
}

// FileCopyRequest 复制文件请求
type FileCopyRequest struct {
	ParentID *uint  `json:"parent_id"`
	Name     string `json:"name" binding:"max=255"`
}

// FileListRequest 文件列表请求
type FileListRequest struct {
	ParentID *uint  `form:"parent_id"`
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
	OrderBy  string `form:"order_by,default=created_at"`
	Order    string `form:"order,default=desc"`
}

// FileSearchRequest 文件搜索请求
type FileSearchRequest struct {
	Keyword  string `form:"keyword" binding:"required"`
	Type     string `form:"type"` // file, folder, all
	Page     int    `form:"page,default=1"`
	PageSize int    `form:"page_size,default=20"`
}

// FileResponse 文件响应
type FileResponse struct {
	ID        uint           `json:"id"`
	UserID    uint           `json:"user_id"`
	ParentID  *uint          `json:"parent_id"`
	Name      string         `json:"name"`
	Type      string         `json:"type"`
	Size      int64          `json:"size"`
	MimeType  string         `json:"mime_type"`
	FileHash  string         `json:"file_hash"`
	IsDeleted bool           `json:"is_deleted"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt *time.Time     `json:"deleted_at"`
	Children  []FileResponse `json:"children,omitempty"`
}

// ToResponse 转换为响应格式
func (f *File) ToResponse() *FileResponse {
	resp := &FileResponse{
		ID:        f.ID,
		UserID:    f.UserID,
		ParentID:  f.ParentID,
		Name:      f.Name,
		Type:      f.Type,
		Size:      f.Size,
		MimeType:  f.MimeType,
		FileHash:  f.FileHash,
		IsDeleted: f.IsDeleted,
		CreatedAt: f.CreatedAt,
		UpdatedAt: f.UpdatedAt,
		DeletedAt: f.DeletedAt,
	}

	// 转换子文件
	if len(f.Children) > 0 {
		resp.Children = make([]FileResponse, len(f.Children))
		for i, child := range f.Children {
			resp.Children[i] = *child.ToResponse()
		}
	}

	return resp
}

// IsFolder 判断是否为文件夹
func (f *File) IsFolder() bool {
	return f.Type == "folder"
}

// IsFile 判断是否为文件
func (f *File) IsFile() bool {
	return f.Type == "file"
}
