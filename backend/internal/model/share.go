package model

import (
	"time"
)

// Share 分享模型
type Share struct {
	ID            uint       `json:"id" gorm:"primaryKey"`
	FileID        uint       `json:"file_id" gorm:"not null;index"`
	UserID        uint       `json:"user_id" gorm:"not null;index"`
	ShareCode     string     `json:"share_code" gorm:"uniqueIndex;size:20;not null"`
	Password      string     `json:"password" gorm:"size:20"`
	ExpireTime    *time.Time `json:"expire_time"`
	DownloadCount int        `json:"download_count" gorm:"default:0"`
	ViewCount     int        `json:"view_count" gorm:"default:0"`
	IsActive      bool       `json:"is_active" gorm:"default:true"`
	CreatedAt     time.Time  `json:"created_at"`
	
	// 关联
	File File `json:"file" gorm:"foreignKey:FileID"`
	User User `json:"user" gorm:"foreignKey:UserID"`
}

// ShareCreateRequest 创建分享请求
type ShareCreateRequest struct {
	FileID     uint   `json:"file_id" binding:"required"`
	Password   string `json:"password" binding:"max=20"`
	ExpireDays int    `json:"expire_days" binding:"min=0,max=365"` // 0表示永不过期
}

// ShareAccessRequest 访问分享请求
type ShareAccessRequest struct {
	Password string `json:"password"`
}

// ShareResponse 分享响应
type ShareResponse struct {
	ID            uint       `json:"id"`
	FileID        uint       `json:"file_id"`
	UserID        uint       `json:"user_id"`
	ShareCode     string     `json:"share_code"`
	HasPassword   bool       `json:"has_password"`
	ExpireTime    *time.Time `json:"expire_time"`
	DownloadCount int        `json:"download_count"`
	ViewCount     int        `json:"view_count"`
	IsActive      bool       `json:"is_active"`
	CreatedAt     time.Time  `json:"created_at"`
	File          *FileResponse `json:"file,omitempty"`
}

// ToResponse 转换为响应格式
func (s *Share) ToResponse() *ShareResponse {
	resp := &ShareResponse{
		ID:            s.ID,
		FileID:        s.FileID,
		UserID:        s.UserID,
		ShareCode:     s.ShareCode,
		HasPassword:   s.Password != "",
		ExpireTime:    s.ExpireTime,
		DownloadCount: s.DownloadCount,
		ViewCount:     s.ViewCount,
		IsActive:      s.IsActive,
		CreatedAt:     s.CreatedAt,
	}
	
	if s.File.ID != 0 {
		resp.File = s.File.ToResponse()
	}
	
	return resp
}

// IsExpired 检查是否过期
func (s *Share) IsExpired() bool {
	if s.ExpireTime == nil {
		return false
	}
	return time.Now().After(*s.ExpireTime)
}

// IsValid 检查分享是否有效
func (s *Share) IsValid() bool {
	return s.IsActive && !s.IsExpired()
}
