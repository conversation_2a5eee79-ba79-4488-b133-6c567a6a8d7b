package utils

import (
	"net/http"
	
	"github.com/gin-gonic/gin"
)

// Response 统一响应结构
type Response struct {
	Code    int         `json:"code"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// PageResponse 分页响应结构
type PageResponse struct {
	List     interface{} `json:"list"`
	Total    int64       `json:"total"`
	Page     int         `json:"page"`
	PageSize int         `json:"page_size"`
}

// 响应码定义
const (
	CodeSuccess = 200
	CodeError   = 500
	
	// 用户相关错误码
	CodeUserNotFound      = 1001
	CodeUserExists        = 1002
	CodeInvalidPassword   = 1003
	CodeUnauthorized      = 1004
	CodeTokenExpired      = 1005
	CodeTokenInvalid      = 1006
	
	// 文件相关错误码
	CodeFileNotFound      = 2001
	CodeFileExists        = 2002
	CodeFileTooBig        = 2003
	CodeInvalidFileType   = 2004
	CodeUploadFailed      = 2005
	CodeDownloadFailed    = 2006
	CodeInsufficientSpace = 2007
	
	// 分享相关错误码
	CodeShareNotFound     = 3001
	CodeShareExpired      = 3002
	CodeSharePasswordWrong = 3003
	CodeShareInactive     = 3004
	
	// 参数错误
	CodeInvalidParams = 4001
	CodeMissingParams = 4002
)

// 错误消息映射
var errorMessages = map[int]string{
	CodeSuccess: "操作成功",
	CodeError:   "操作失败",
	
	CodeUserNotFound:      "用户不存在",
	CodeUserExists:        "用户已存在",
	CodeInvalidPassword:   "密码错误",
	CodeUnauthorized:      "未授权访问",
	CodeTokenExpired:      "登录已过期",
	CodeTokenInvalid:      "无效的登录凭证",
	
	CodeFileNotFound:      "文件不存在",
	CodeFileExists:        "文件已存在",
	CodeFileTooBig:        "文件过大",
	CodeInvalidFileType:   "不支持的文件类型",
	CodeUploadFailed:      "文件上传失败",
	CodeDownloadFailed:    "文件下载失败",
	CodeInsufficientSpace: "存储空间不足",
	
	CodeShareNotFound:      "分享不存在",
	CodeShareExpired:       "分享已过期",
	CodeSharePasswordWrong: "分享密码错误",
	CodeShareInactive:      "分享已失效",
	
	CodeInvalidParams: "参数错误",
	CodeMissingParams: "缺少必要参数",
}

// Success 成功响应
func Success(c *gin.Context, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: errorMessages[CodeSuccess],
		Data:    data,
	})
}

// SuccessWithMessage 带自定义消息的成功响应
func SuccessWithMessage(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: message,
		Data:    data,
	})
}

// Error 错误响应
func Error(c *gin.Context, code int) {
	message := errorMessages[code]
	if message == "" {
		message = errorMessages[CodeError]
	}
	
	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// ErrorWithMessage 带自定义消息的错误响应
func ErrorWithMessage(c *gin.Context, code int, message string) {
	c.JSON(http.StatusOK, Response{
		Code:    code,
		Message: message,
	})
}

// BadRequest 参数错误响应
func BadRequest(c *gin.Context, message string) {
	c.JSON(http.StatusBadRequest, Response{
		Code:    CodeInvalidParams,
		Message: message,
	})
}

// Unauthorized 未授权响应
func Unauthorized(c *gin.Context) {
	c.JSON(http.StatusUnauthorized, Response{
		Code:    CodeUnauthorized,
		Message: errorMessages[CodeUnauthorized],
	})
}

// InternalServerError 服务器错误响应
func InternalServerError(c *gin.Context) {
	c.JSON(http.StatusInternalServerError, Response{
		Code:    CodeError,
		Message: errorMessages[CodeError],
	})
}

// PageSuccess 分页成功响应
func PageSuccess(c *gin.Context, list interface{}, total int64, page, pageSize int) {
	c.JSON(http.StatusOK, Response{
		Code:    CodeSuccess,
		Message: errorMessages[CodeSuccess],
		Data: PageResponse{
			List:     list,
			Total:    total,
			Page:     page,
			PageSize: pageSize,
		},
	})
}
