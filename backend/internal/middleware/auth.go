package middleware

import (
	"strings"
	
	"jiangjiang-netdisk/internal/config"
	"jiangjiang-netdisk/internal/utils"
	
	"github.com/gin-gonic/gin"
)

// AuthMiddleware JWT认证中间件
func AuthMiddleware(cfg *config.Config) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.Get<PERSON>eader("Authorization")
		if authHeader == "" {
			utils.Unauthorized(c)
			c.Abort()
			return
		}
		
		// 检查Bearer前缀
		if !strings.HasPrefix(authHeader, "Bearer ") {
			utils.Unauthorized(c)
			c.Abort()
			return
		}
		
		// 提取token
		token := strings.TrimPrefix(authHeader, "Bearer ")
		if token == "" {
			utils.Unauthorized(c)
			c.Abort()
			return
		}
		
		// 解析token
		claims, err := utils.ParseToken(token, cfg.JWT.Secret)
		if err != nil {
			utils.Error(c, utils.CodeTokenInvalid)
			c.Abort()
			return
		}
		
		// 将用户信息存储到上下文
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		
		c.Next()
	}
}

// GetCurrentUserID 从上下文获取当前用户ID
func GetCurrentUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		return userID.(uint)
	}
	return 0
}

// GetCurrentUsername 从上下文获取当前用户名
func GetCurrentUsername(c *gin.Context) string {
	if username, exists := c.Get("username"); exists {
		return username.(string)
	}
	return ""
}
