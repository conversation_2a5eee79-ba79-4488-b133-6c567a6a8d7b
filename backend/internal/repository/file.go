package repository

import (
	"jiangjiang-netdisk/internal/model"
	
	"gorm.io/gorm"
)

type FileRepository struct {
	db *gorm.DB
}

func NewFileRepository(db *gorm.DB) *FileRepository {
	return &FileRepository{db: db}
}

// Create 创建文件记录
func (r *FileRepository) Create(file *model.File) error {
	return r.db.Create(file).Error
}

// GetByID 根据ID获取文件
func (r *FileRepository) GetByID(id uint) (*model.File, error) {
	var file model.File
	err := r.db.Where("is_deleted = ?", false).First(&file, id).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetByIDWithUser 根据ID获取文件（包含用户验证）
func (r *FileRepository) GetByIDWithUser(id, userID uint) (*model.File, error) {
	var file model.File
	err := r.db.Where("id = ? AND user_id = ? AND is_deleted = ?", id, userID, false).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// GetList 获取文件列表
func (r *FileRepository) GetList(userID uint, parentID *uint, page, pageSize int, orderBy, order string) ([]model.File, int64, error) {
	var files []model.File
	var total int64
	
	query := r.db.Where("user_id = ? AND is_deleted = ?", userID, false)
	
	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	} else {
		query = query.Where("parent_id IS NULL")
	}
	
	// 计算总数
	err := query.Model(&model.File{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Order(orderBy + " " + order).Offset(offset).Limit(pageSize).Find(&files).Error
	
	return files, total, err
}

// Search 搜索文件
func (r *FileRepository) Search(userID uint, keyword, fileType string, page, pageSize int) ([]model.File, int64, error) {
	var files []model.File
	var total int64
	
	query := r.db.Where("user_id = ? AND is_deleted = ? AND name ILIKE ?", userID, false, "%"+keyword+"%")
	
	if fileType != "" && fileType != "all" {
		query = query.Where("type = ?", fileType)
	}
	
	// 计算总数
	err := query.Model(&model.File{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&files).Error
	
	return files, total, err
}

// Update 更新文件信息
func (r *FileRepository) Update(file *model.File) error {
	return r.db.Save(file).Error
}

// Delete 软删除文件
func (r *FileRepository) Delete(id, userID uint) error {
	return r.db.Model(&model.File{}).Where("id = ? AND user_id = ?", id, userID).
		Update("is_deleted", true).Error
}

// GetByHash 根据哈希值获取文件（用于去重）
func (r *FileRepository) GetByHash(hash string, userID uint) (*model.File, error) {
	var file model.File
	err := r.db.Where("file_hash = ? AND user_id = ? AND is_deleted = ?", hash, userID, false).First(&file).Error
	if err != nil {
		return nil, err
	}
	return &file, nil
}

// ExistsByName 检查同一目录下是否存在同名文件
func (r *FileRepository) ExistsByName(userID uint, parentID *uint, name string, excludeID uint) (bool, error) {
	var count int64
	query := r.db.Model(&model.File{}).Where("user_id = ? AND name = ? AND is_deleted = ?", userID, name, false)
	
	if parentID != nil {
		query = query.Where("parent_id = ?", *parentID)
	} else {
		query = query.Where("parent_id IS NULL")
	}
	
	if excludeID > 0 {
		query = query.Where("id != ?", excludeID)
	}
	
	err := query.Count(&count).Error
	return count > 0, err
}

// GetChildren 获取文件夹下的所有子文件（递归）
func (r *FileRepository) GetChildren(parentID uint) ([]model.File, error) {
	var files []model.File
	err := r.db.Where("parent_id = ? AND is_deleted = ?", parentID, false).Find(&files).Error
	return files, err
}

// GetDeletedFiles 获取回收站文件
func (r *FileRepository) GetDeletedFiles(userID uint, page, pageSize int) ([]model.File, int64, error) {
	var files []model.File
	var total int64
	
	query := r.db.Where("user_id = ? AND is_deleted = ?", userID, true)
	
	// 计算总数
	err := query.Model(&model.File{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Order("updated_at DESC").Offset(offset).Limit(pageSize).Find(&files).Error
	
	return files, total, err
}

// Restore 恢复文件
func (r *FileRepository) Restore(id, userID uint) error {
	return r.db.Model(&model.File{}).Where("id = ? AND user_id = ?", id, userID).
		Update("is_deleted", false).Error
}

// PermanentDelete 永久删除文件
func (r *FileRepository) PermanentDelete(id, userID uint) error {
	return r.db.Where("id = ? AND user_id = ?", id, userID).Delete(&model.File{}).Error
}
