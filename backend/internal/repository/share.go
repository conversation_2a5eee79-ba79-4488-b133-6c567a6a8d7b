package repository

import (
	"jiangjiang-netdisk/internal/model"
	
	"gorm.io/gorm"
)

type ShareRepository struct {
	db *gorm.DB
}

func NewShareRepository(db *gorm.DB) *ShareRepository {
	return &ShareRepository{db: db}
}

// Create 创建分享
func (r *ShareRepository) Create(share *model.Share) error {
	return r.db.Create(share).Error
}

// GetByCode 根据分享码获取分享
func (r *ShareRepository) GetByCode(code string) (*model.Share, error) {
	var share model.Share
	err := r.db.Preload("File").Preload("User").Where("share_code = ?", code).First(&share).Error
	if err != nil {
		return nil, err
	}
	return &share, nil
}

// GetByID 根据ID获取分享
func (r *ShareRepository) GetByID(id uint) (*model.Share, error) {
	var share model.Share
	err := r.db.Preload("File").First(&share, id).Error
	if err != nil {
		return nil, err
	}
	return &share, nil
}

// GetByUser 获取用户的分享列表
func (r *ShareRepository) GetByUser(userID uint, page, pageSize int) ([]model.Share, int64, error) {
	var shares []model.Share
	var total int64
	
	query := r.db.Where("user_id = ?", userID)
	
	// 计算总数
	err := query.Model(&model.Share{}).Count(&total).Error
	if err != nil {
		return nil, 0, err
	}
	
	// 获取分页数据
	offset := (page - 1) * pageSize
	err = query.Preload("File").Order("created_at DESC").Offset(offset).Limit(pageSize).Find(&shares).Error
	
	return shares, total, err
}

// Update 更新分享
func (r *ShareRepository) Update(share *model.Share) error {
	return r.db.Save(share).Error
}

// Delete 删除分享
func (r *ShareRepository) Delete(id, userID uint) error {
	return r.db.Where("id = ? AND user_id = ?", id, userID).Delete(&model.Share{}).Error
}

// IncrementViewCount 增加查看次数
func (r *ShareRepository) IncrementViewCount(id uint) error {
	return r.db.Model(&model.Share{}).Where("id = ?", id).
		Update("view_count", gorm.Expr("view_count + 1")).Error
}

// IncrementDownloadCount 增加下载次数
func (r *ShareRepository) IncrementDownloadCount(id uint) error {
	return r.db.Model(&model.Share{}).Where("id = ?", id).
		Update("download_count", gorm.Expr("download_count + 1")).Error
}

// ExistsByCode 检查分享码是否存在
func (r *ShareRepository) ExistsByCode(code string) (bool, error) {
	var count int64
	err := r.db.Model(&model.Share{}).Where("share_code = ?", code).Count(&count).Error
	return count > 0, err
}
