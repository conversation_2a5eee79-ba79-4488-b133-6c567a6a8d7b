package handler

import (
	"strconv"
	
	"jiangjiang-netdisk/internal/middleware"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/service"
	"jiangjiang-netdisk/internal/utils"
	
	"github.com/gin-gonic/gin"
)

type ShareHandler struct {
	shareService *service.ShareService
}

func NewShareHandler(shareService *service.ShareService) *ShareHandler {
	return &ShareHandler{
		shareService: shareService,
	}
}

// Create 创建分享
func (h *ShareHandler) Create(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	var req model.ShareCreateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	share, err := h.shareService.Create(userID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "分享创建成功", share)
}

// GetByCode 根据分享码获取分享信息
func (h *ShareHandler) GetByCode(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		utils.BadRequest(c, "分享码不能为空")
		return
	}
	
	share, err := h.shareService.GetByCode(code)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeShareNotFound, err.Error())
		return
	}
	
	utils.Success(c, share)
}

// Access 访问分享（验证密码）
func (h *ShareHandler) Access(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		utils.BadRequest(c, "分享码不能为空")
		return
	}
	
	var req model.ShareAccessRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	share, err := h.shareService.Access(code, &req)
	if err != nil {
		if err.Error() == "密码错误" {
			utils.ErrorWithMessage(c, utils.CodeSharePasswordWrong, err.Error())
		} else {
			utils.ErrorWithMessage(c, utils.CodeShareNotFound, err.Error())
		}
		return
	}
	
	utils.Success(c, share)
}

// Download 下载分享文件
func (h *ShareHandler) Download(c *gin.Context) {
	code := c.Param("code")
	if code == "" {
		utils.BadRequest(c, "分享码不能为空")
		return
	}
	
	password := c.Query("password")
	
	file, err := h.shareService.Download(code, password)
	if err != nil {
		if err.Error() == "密码错误" {
			utils.ErrorWithMessage(c, utils.CodeSharePasswordWrong, err.Error())
		} else {
			utils.ErrorWithMessage(c, utils.CodeShareNotFound, err.Error())
		}
		return
	}
	
	// 这里应该返回文件下载，但为了简化，先返回文件信息
	utils.Success(c, file.ToResponse())
}

// GetUserShares 获取用户的分享列表
func (h *ShareHandler) GetUserShares(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))
	
	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}
	
	shares, total, err := h.shareService.GetUserShares(userID, page, pageSize)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.PageSuccess(c, shares, total, page, pageSize)
}

// Cancel 取消分享
func (h *ShareHandler) Cancel(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	shareIDStr := c.Param("id")
	shareID, err := strconv.ParseUint(shareIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的分享ID")
		return
	}
	
	err = h.shareService.Cancel(userID, uint(shareID))
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "分享已取消", nil)
}

// UpdateStatus 更新分享状态
func (h *ShareHandler) UpdateStatus(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	shareIDStr := c.Param("id")
	shareID, err := strconv.ParseUint(shareIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的分享ID")
		return
	}
	
	var req struct {
		IsActive bool `json:"is_active"`
	}
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	share, err := h.shareService.UpdateStatus(userID, uint(shareID), req.IsActive)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "状态更新成功", share)
}
