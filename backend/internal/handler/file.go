package handler

import (
	"net/http"
	"path/filepath"
	"strconv"
	
	"jiangjiang-netdisk/internal/middleware"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/service"
	"jiangjiang-netdisk/internal/utils"
	
	"github.com/gin-gonic/gin"
)

type FileHandler struct {
	fileService *service.FileService
}

func NewFileHandler(fileService *service.FileService) *FileHandler {
	return &FileHandler{
		fileService: fileService,
	}
}

// GetList 获取文件列表
func (h *FileHandler) GetList(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	var req model.FileListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.OrderBy == "" {
		req.OrderBy = "created_at"
	}
	if req.Order == "" {
		req.Order = "desc"
	}
	
	files, total, err := h.fileService.GetList(userID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.PageSuccess(c, files, total, req.Page, req.PageSize)
}

// CreateFolder 创建文件夹
func (h *FileHandler) CreateFolder(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	var req model.FileCreateFolderRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	folder, err := h.fileService.CreateFolder(userID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "文件夹创建成功", folder)
}

// Upload 文件上传
func (h *FileHandler) Upload(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	// 获取上传的文件
	fileHeader, err := c.FormFile("file")
	if err != nil {
		utils.BadRequest(c, "请选择要上传的文件")
		return
	}
	
	// 获取其他参数
	var req model.FileUploadRequest
	req.Name = fileHeader.Filename
	if parentIDStr := c.PostForm("parent_id"); parentIDStr != "" {
		if parentID, err := strconv.ParseUint(parentIDStr, 10, 32); err == nil {
			id := uint(parentID)
			req.ParentID = &id
		}
	}
	
	file, err := h.fileService.Upload(userID, fileHeader, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeUploadFailed, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "文件上传成功", file)
}

// Download 文件下载
func (h *FileHandler) Download(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}
	
	file, filePath, err := h.fileService.Download(userID, uint(fileID))
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeFileNotFound, err.Error())
		return
	}
	
	// 设置响应头
	c.Header("Content-Description", "File Transfer")
	c.Header("Content-Transfer-Encoding", "binary")
	c.Header("Content-Disposition", "attachment; filename="+file.Name)
	c.Header("Content-Type", file.MimeType)
	
	c.File(filePath)
}

// Rename 重命名文件/文件夹
func (h *FileHandler) Rename(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}
	
	var req model.FileRenameRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	file, err := h.fileService.Rename(userID, uint(fileID), &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "重命名成功", file)
}

// Move 移动文件/文件夹
func (h *FileHandler) Move(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}
	
	var req model.FileMoveRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	file, err := h.fileService.Move(userID, uint(fileID), &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "移动成功", file)
}

// Copy 复制文件/文件夹
func (h *FileHandler) Copy(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}
	
	var req model.FileCopyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	file, err := h.fileService.Copy(userID, uint(fileID), &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "复制成功", file)
}

// Delete 删除文件/文件夹
func (h *FileHandler) Delete(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}
	
	err = h.fileService.Delete(userID, uint(fileID))
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "删除成功", nil)
}

// Search 搜索文件
func (h *FileHandler) Search(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}

	var req model.FileSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}

	// 设置默认值
	if req.Page <= 0 {
		req.Page = 1
	}
	if req.PageSize <= 0 {
		req.PageSize = 20
	}
	if req.Type == "" {
		req.Type = "all"
	}

	files, total, err := h.fileService.Search(userID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}

	utils.PageSuccess(c, files, total, req.Page, req.PageSize)
}

// GetDeletedFiles 获取回收站文件
func (h *FileHandler) GetDeletedFiles(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}

	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "20"))

	if page <= 0 {
		page = 1
	}
	if pageSize <= 0 {
		pageSize = 20
	}

	files, total, err := h.fileService.GetDeletedFiles(userID, page, pageSize)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}

	utils.PageSuccess(c, files, total, page, pageSize)
}

// Restore 恢复文件
func (h *FileHandler) Restore(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}

	file, err := h.fileService.Restore(userID, uint(fileID))
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "恢复成功", file)
}

// PermanentDelete 永久删除文件
func (h *FileHandler) PermanentDelete(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}

	fileIDStr := c.Param("id")
	fileID, err := strconv.ParseUint(fileIDStr, 10, 32)
	if err != nil {
		utils.BadRequest(c, "无效的文件ID")
		return
	}

	err = h.fileService.PermanentDelete(userID, uint(fileID))
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}

	utils.SuccessWithMessage(c, "永久删除成功", nil)
}
