package handler

import (
	"jiangjiang-netdisk/internal/middleware"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/service"
	"jiangjiang-netdisk/internal/utils"
	
	"github.com/gin-gonic/gin"
)

type UserHandler struct {
	userService *service.UserService
}

func NewUserHandler(userService *service.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// Register 用户注册
func (h *UserHandler) Register(c *gin.Context) {
	var req model.UserRegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	user, err := h.userService.Register(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "注册成功", user)
}

// Login 用户登录
func (h *UserHandler) Login(c *gin.Context) {
	var req model.UserLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	token, user, err := h.userService.Login(&req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInvalidPassword, err.Error())
		return
	}
	
	utils.Success(c, gin.H{
		"token": token,
		"user":  user,
	})
}

// GetProfile 获取用户信息
func (h *UserHandler) GetProfile(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	user, err := h.userService.GetProfile(userID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeUserNotFound, err.Error())
		return
	}
	
	utils.Success(c, user)
}

// UpdateProfile 更新用户信息
func (h *UserHandler) UpdateProfile(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	var req model.UserUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	user, err := h.userService.UpdateProfile(userID, &req)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "更新成功", user)
}

// ChangePassword 修改密码
func (h *UserHandler) ChangePassword(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	var req struct {
		OldPassword string `json:"old_password" binding:"required"`
		NewPassword string `json:"new_password" binding:"required,min=6,max=50"`
	}
	
	if err := c.ShouldBindJSON(&req); err != nil {
		utils.BadRequest(c, err.Error())
		return
	}
	
	err := h.userService.ChangePassword(userID, req.OldPassword, req.NewPassword)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeInvalidPassword, err.Error())
		return
	}
	
	utils.SuccessWithMessage(c, "密码修改成功", nil)
}

// GetStorageInfo 获取存储空间信息
func (h *UserHandler) GetStorageInfo(c *gin.Context) {
	userID := middleware.GetCurrentUserID(c)
	if userID == 0 {
		utils.Unauthorized(c)
		return
	}
	
	info, err := h.userService.GetStorageInfo(userID)
	if err != nil {
		utils.ErrorWithMessage(c, utils.CodeError, err.Error())
		return
	}
	
	utils.Success(c, info)
}
