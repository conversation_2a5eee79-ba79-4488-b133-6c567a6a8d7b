package service

import (
	"errors"
	"time"
	
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/repository"
	"jiangjiang-netdisk/internal/utils"
	
	"gorm.io/gorm"
)

type ShareService struct {
	shareRepo *repository.ShareRepository
	fileRepo  *repository.FileRepository
}

func NewShareService(shareRepo *repository.ShareRepository, fileRepo *repository.FileRepository) *ShareService {
	return &ShareService{
		shareRepo: shareRepo,
		fileRepo:  fileRepo,
	}
}

// Create 创建分享
func (s *ShareService) Create(userID uint, req *model.ShareCreateRequest) (*model.ShareResponse, error) {
	// 验证文件是否存在且属于当前用户
	file, err := s.fileRepo.GetByIDWithUser(req.FileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}
	
	// 生成唯一的分享码
	var shareCode string
	for {
		shareCode = utils.GenerateShareCode()
		exists, err := s.shareRepo.ExistsByCode(shareCode)
		if err != nil {
			return nil, err
		}
		if !exists {
			break
		}
	}
	
	// 计算过期时间
	var expireTime *time.Time
	if req.ExpireDays > 0 {
		expire := time.Now().AddDate(0, 0, req.ExpireDays)
		expireTime = &expire
	}
	
	// 创建分享记录
	share := &model.Share{
		FileID:     req.FileID,
		UserID:     userID,
		ShareCode:  shareCode,
		Password:   req.Password,
		ExpireTime: expireTime,
		IsActive:   true,
	}
	
	err = s.shareRepo.Create(share)
	if err != nil {
		return nil, err
	}
	
	// 重新获取分享信息（包含关联数据）
	share, err = s.shareRepo.GetByCode(shareCode)
	if err != nil {
		return nil, err
	}
	
	return share.ToResponse(), nil
}

// GetByCode 根据分享码获取分享信息
func (s *ShareService) GetByCode(code string) (*model.ShareResponse, error) {
	share, err := s.shareRepo.GetByCode(code)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("分享不存在")
		}
		return nil, err
	}
	
	// 检查分享是否有效
	if !share.IsValid() {
		return nil, errors.New("分享已失效")
	}
	
	// 增加查看次数
	s.shareRepo.IncrementViewCount(share.ID)
	
	return share.ToResponse(), nil
}

// Access 访问分享（验证密码）
func (s *ShareService) Access(code string, req *model.ShareAccessRequest) (*model.ShareResponse, error) {
	share, err := s.shareRepo.GetByCode(code)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("分享不存在")
		}
		return nil, err
	}
	
	// 检查分享是否有效
	if !share.IsValid() {
		return nil, errors.New("分享已失效")
	}
	
	// 验证密码
	if share.Password != "" && share.Password != req.Password {
		return nil, errors.New("密码错误")
	}
	
	return share.ToResponse(), nil
}

// Download 下载分享文件
func (s *ShareService) Download(code string, password string) (*model.File, error) {
	share, err := s.shareRepo.GetByCode(code)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("分享不存在")
		}
		return nil, err
	}
	
	// 检查分享是否有效
	if !share.IsValid() {
		return nil, errors.New("分享已失效")
	}
	
	// 验证密码
	if share.Password != "" && share.Password != password {
		return nil, errors.New("密码错误")
	}
	
	// 增加下载次数
	s.shareRepo.IncrementDownloadCount(share.ID)
	
	return &share.File, nil
}

// GetUserShares 获取用户的分享列表
func (s *ShareService) GetUserShares(userID uint, page, pageSize int) ([]model.ShareResponse, int64, error) {
	shares, total, err := s.shareRepo.GetByUser(userID, page, pageSize)
	if err != nil {
		return nil, 0, err
	}
	
	responses := make([]model.ShareResponse, len(shares))
	for i, share := range shares {
		responses[i] = *share.ToResponse()
	}
	
	return responses, total, nil
}

// Cancel 取消分享
func (s *ShareService) Cancel(userID, shareID uint) error {
	share, err := s.shareRepo.GetByID(shareID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("分享不存在")
		}
		return err
	}
	
	// 检查分享是否属于当前用户
	if share.UserID != userID {
		return errors.New("无权限操作")
	}
	
	return s.shareRepo.Delete(shareID, userID)
}

// UpdateStatus 更新分享状态
func (s *ShareService) UpdateStatus(userID, shareID uint, isActive bool) (*model.ShareResponse, error) {
	share, err := s.shareRepo.GetByID(shareID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("分享不存在")
		}
		return nil, err
	}
	
	// 检查分享是否属于当前用户
	if share.UserID != userID {
		return nil, errors.New("无权限操作")
	}
	
	share.IsActive = isActive
	err = s.shareRepo.Update(share)
	if err != nil {
		return nil, err
	}
	
	return share.ToResponse(), nil
}
