package service

import (
	"errors"
	
	"jiangjiang-netdisk/internal/config"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/repository"
	"jiangjiang-netdisk/internal/utils"
	
	"gorm.io/gorm"
)

type UserService struct {
	userRepo *repository.UserRepository
	cfg      *config.Config
}

func NewUserService(userRepo *repository.UserRepository, cfg *config.Config) *UserService {
	return &UserService{
		userRepo: userRepo,
		cfg:      cfg,
	}
}

// Register 用户注册
func (s *UserService) Register(req *model.UserRegisterRequest) (*model.UserResponse, error) {
	// 检查用户名是否已存在
	exists, err := s.userRepo.ExistsByUsername(req.Username)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("用户名已存在")
	}
	
	// 检查邮箱是否已存在
	exists, err = s.userRepo.ExistsByEmail(req.Email)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("邮箱已存在")
	}
	
	// 加密密码
	hashedPassword, err := utils.HashPassword(req.Password)
	if err != nil {
		return nil, err
	}
	
	// 创建用户
	user := &model.User{
		Username:     req.Username,
		Email:        req.Email,
		PasswordHash: hashedPassword,
		Nickname:     req.Nickname,
		TotalSpace:   10737418240, // 10GB
		UsedSpace:    0,
		Status:       1,
	}
	
	if user.Nickname == "" {
		user.Nickname = user.Username
	}
	
	err = s.userRepo.Create(user)
	if err != nil {
		return nil, err
	}
	
	return user.ToResponse(), nil
}

// Login 用户登录
func (s *UserService) Login(req *model.UserLoginRequest) (string, *model.UserResponse, error) {
	// 获取用户信息
	user, err := s.userRepo.GetByUsername(req.Username)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return "", nil, errors.New("用户名或密码错误")
		}
		return "", nil, err
	}
	
	// 检查用户状态
	if user.Status != 1 {
		return "", nil, errors.New("用户已被禁用")
	}
	
	// 验证密码
	if !utils.CheckPassword(req.Password, user.PasswordHash) {
		return "", nil, errors.New("用户名或密码错误")
	}
	
	// 生成JWT token
	token, err := utils.GenerateToken(user.ID, user.Username, s.cfg.JWT.Secret, s.cfg.JWT.ExpireHour)
	if err != nil {
		return "", nil, err
	}
	
	return token, user.ToResponse(), nil
}

// GetProfile 获取用户信息
func (s *UserService) GetProfile(userID uint) (*model.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	
	return user.ToResponse(), nil
}

// UpdateProfile 更新用户信息
func (s *UserService) UpdateProfile(userID uint, req *model.UserUpdateRequest) (*model.UserResponse, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	
	// 更新字段
	if req.Nickname != "" {
		user.Nickname = req.Nickname
	}
	if req.AvatarURL != "" {
		user.AvatarURL = req.AvatarURL
	}
	
	err = s.userRepo.Update(user)
	if err != nil {
		return nil, err
	}
	
	return user.ToResponse(), nil
}

// ChangePassword 修改密码
func (s *UserService) ChangePassword(userID uint, oldPassword, newPassword string) error {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("用户不存在")
		}
		return err
	}
	
	// 验证旧密码
	if !utils.CheckPassword(oldPassword, user.PasswordHash) {
		return errors.New("原密码错误")
	}
	
	// 加密新密码
	hashedPassword, err := utils.HashPassword(newPassword)
	if err != nil {
		return err
	}
	
	user.PasswordHash = hashedPassword
	return s.userRepo.Update(user)
}

// GetStorageInfo 获取存储空间信息
func (s *UserService) GetStorageInfo(userID uint) (map[string]interface{}, error) {
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("用户不存在")
		}
		return nil, err
	}
	
	return map[string]interface{}{
		"total_space":      user.TotalSpace,
		"used_space":       user.UsedSpace,
		"available_space":  user.TotalSpace - user.UsedSpace,
		"usage_percentage": float64(user.UsedSpace) / float64(user.TotalSpace) * 100,
		"total_space_str":  utils.FormatFileSize(user.TotalSpace),
		"used_space_str":   utils.FormatFileSize(user.UsedSpace),
		"available_space_str": utils.FormatFileSize(user.TotalSpace - user.UsedSpace),
	}, nil
}
