package service

import (
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path/filepath"

	"jiangjiang-netdisk/internal/config"
	"jiangjiang-netdisk/internal/model"
	"jiangjiang-netdisk/internal/repository"
	"jiangjiang-netdisk/internal/utils"

	"gorm.io/gorm"
)

type FileService struct {
	fileRepo *repository.FileRepository
	userRepo *repository.UserRepository
	cfg      *config.Config
	db       *gorm.DB
}

func NewFileService(fileRepo *repository.FileRepository, userRepo *repository.UserRepository, cfg *config.Config, db *gorm.DB) *FileService {
	return &FileService{
		fileRepo: fileRepo,
		userRepo: userRepo,
		cfg:      cfg,
		db:       db,
	}
}

// GetList 获取文件列表
func (s *FileService) GetList(userID uint, req *model.FileListRequest) ([]model.FileResponse, int64, error) {
	files, total, err := s.fileRepo.GetList(userID, req.ParentID, req.Page, req.PageSize, req.OrderBy, req.Order)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]model.FileResponse, len(files))
	for i, file := range files {
		responses[i] = *file.ToResponse()
	}

	return responses, total, nil
}

// CreateFolder 创建文件夹
func (s *FileService) CreateFolder(userID uint, req *model.FileCreateFolderRequest) (*model.FileResponse, error) {
	// 检查同名文件夹是否存在
	exists, err := s.fileRepo.ExistsByName(userID, req.ParentID, req.Name, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("文件夹已存在")
	}

	// 如果有父文件夹，验证父文件夹是否存在且属于当前用户
	if req.ParentID != nil {
		parent, err := s.fileRepo.GetByIDWithUser(*req.ParentID, userID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("父文件夹不存在")
			}
			return nil, err
		}
		if !parent.IsFolder() {
			return nil, errors.New("父级必须是文件夹")
		}
	}

	// 创建文件夹
	folder := &model.File{
		UserID:   userID,
		ParentID: req.ParentID,
		Name:     req.Name,
		Type:     "folder",
		Size:     0,
	}

	err = s.fileRepo.Create(folder)
	if err != nil {
		return nil, err
	}

	return folder.ToResponse(), nil
}

// Upload 文件上传
func (s *FileService) Upload(userID uint, fileHeader *multipart.FileHeader, req *model.FileUploadRequest) (*model.FileResponse, error) {
	// 检查文件大小
	if fileHeader.Size > s.cfg.Storage.MaxSize {
		return nil, errors.New("文件过大")
	}

	// 检查用户存储空间
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, err
	}
	if user.UsedSpace+fileHeader.Size > user.TotalSpace {
		return nil, errors.New("存储空间不足")
	}

	// 检查同名文件是否存在
	exists, err := s.fileRepo.ExistsByName(userID, req.ParentID, req.Name, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("文件已存在")
	}

	// 打开上传的文件
	src, err := fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()

	// 计算文件哈希
	hash, err := utils.CalculateFileMD5(src)
	if err != nil {
		return nil, err
	}

	// 检查是否已存在相同哈希的文件（去重）
	existingFile, err := s.fileRepo.GetByHash(hash, userID)
	if err == nil && existingFile != nil {
		// 文件已存在，创建硬链接
		newFile := &model.File{
			UserID:   userID,
			ParentID: req.ParentID,
			Name:     req.Name,
			Type:     "file",
			Size:     fileHeader.Size,
			MimeType: utils.GetMimeType(req.Name),
			FilePath: existingFile.FilePath,
			FileHash: hash,
		}

		err = s.fileRepo.Create(newFile)
		if err != nil {
			return nil, err
		}

		// 更新用户已使用空间（虽然是硬链接，但仍计入用户空间）
		err = s.userRepo.UpdateUsedSpace(userID, fileHeader.Size)
		if err != nil {
			return nil, err
		}

		return newFile.ToResponse(), nil
	}

	// 生成存储路径
	storagePath := s.generateStoragePath(userID, hash, req.Name)
	fullPath := filepath.Join(s.cfg.Storage.BasePath, storagePath)

	// 确保目录存在
	err = os.MkdirAll(filepath.Dir(fullPath), 0755)
	if err != nil {
		return nil, err
	}

	// 重新打开文件进行保存
	src, err = fileHeader.Open()
	if err != nil {
		return nil, err
	}
	defer src.Close()

	// 创建目标文件
	dst, err := os.Create(fullPath)
	if err != nil {
		return nil, err
	}
	defer dst.Close()

	// 复制文件内容
	_, err = io.Copy(dst, src)
	if err != nil {
		os.Remove(fullPath) // 清理失败的文件
		return nil, err
	}

	// 创建文件记录
	file := &model.File{
		UserID:   userID,
		ParentID: req.ParentID,
		Name:     req.Name,
		Type:     "file",
		Size:     fileHeader.Size,
		MimeType: utils.GetMimeType(req.Name),
		FilePath: storagePath,
		FileHash: hash,
	}

	err = s.fileRepo.Create(file)
	if err != nil {
		os.Remove(fullPath) // 清理失败的文件
		return nil, err
	}

	// 更新用户已使用空间
	err = s.userRepo.UpdateUsedSpace(userID, fileHeader.Size)
	if err != nil {
		return nil, err
	}

	return file.ToResponse(), nil
}

// generateStoragePath 生成存储路径
func (s *FileService) generateStoragePath(userID uint, hash, filename string) string {
	ext := utils.GetFileExtension(filename)
	if ext != "" {
		ext = "." + ext
	}
	return fmt.Sprintf("users/%d/%s/%s%s", userID, hash[:2], hash, ext)
}

// Download 文件下载
func (s *FileService) Download(userID, fileID uint) (*model.File, string, error) {
	file, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, "", errors.New("文件不存在")
		}
		return nil, "", err
	}

	if file.IsFolder() {
		return nil, "", errors.New("不能下载文件夹")
	}

	fullPath := filepath.Join(s.cfg.Storage.BasePath, file.FilePath)
	if _, err := os.Stat(fullPath); os.IsNotExist(err) {
		return nil, "", errors.New("文件不存在")
	}

	return file, fullPath, nil
}

// Rename 重命名文件/文件夹
func (s *FileService) Rename(userID, fileID uint, req *model.FileRenameRequest) (*model.FileResponse, error) {
	file, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	// 检查同名文件是否存在
	exists, err := s.fileRepo.ExistsByName(userID, file.ParentID, req.Name, fileID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("文件名已存在")
	}

	file.Name = req.Name
	if file.IsFile() {
		file.MimeType = utils.GetMimeType(req.Name)
	}

	err = s.fileRepo.Update(file)
	if err != nil {
		return nil, err
	}

	return file.ToResponse(), nil
}

// Move 移动文件/文件夹
func (s *FileService) Move(userID, fileID uint, req *model.FileMoveRequest) (*model.FileResponse, error) {
	file, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	// 如果有目标文件夹，验证目标文件夹
	if req.ParentID != nil {
		parent, err := s.fileRepo.GetByIDWithUser(*req.ParentID, userID)
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, errors.New("目标文件夹不存在")
			}
			return nil, err
		}
		if !parent.IsFolder() {
			return nil, errors.New("目标必须是文件夹")
		}

		// 检查是否移动到自己的子文件夹（防止循环）
		if file.IsFolder() && s.isDescendant(fileID, *req.ParentID) {
			return nil, errors.New("不能移动到自己的子文件夹")
		}
	}

	// 检查目标位置是否有同名文件
	exists, err := s.fileRepo.ExistsByName(userID, req.ParentID, file.Name, fileID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("目标位置已存在同名文件")
	}

	file.ParentID = req.ParentID
	err = s.fileRepo.Update(file)
	if err != nil {
		return nil, err
	}

	return file.ToResponse(), nil
}

// isDescendant 检查是否为子文件夹
func (s *FileService) isDescendant(parentID, childID uint) bool {
	if parentID == childID {
		return true
	}

	children, err := s.fileRepo.GetChildren(parentID)
	if err != nil {
		return false
	}

	for _, child := range children {
		if child.ID == childID || (child.IsFolder() && s.isDescendant(child.ID, childID)) {
			return true
		}
	}

	return false
}

// Copy 复制文件/文件夹
func (s *FileService) Copy(userID, fileID uint, req *model.FileCopyRequest) (*model.FileResponse, error) {
	file, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	// 确定新文件名
	newName := req.Name
	if newName == "" {
		newName = "副本_" + file.Name
	}

	// 检查目标位置是否有同名文件
	exists, err := s.fileRepo.ExistsByName(userID, req.ParentID, newName, 0)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("目标位置已存在同名文件")
	}

	// 如果是文件夹，递归复制
	if file.IsFolder() {
		return s.copyFolder(userID, file, req.ParentID, newName)
	}

	// 复制文件
	return s.copyFile(userID, file, req.ParentID, newName)
}

// copyFile 复制文件
func (s *FileService) copyFile(userID uint, file *model.File, parentID *uint, newName string) (*model.FileResponse, error) {
	// 检查用户存储空间
	user, err := s.userRepo.GetByID(userID)
	if err != nil {
		return nil, err
	}
	if user.UsedSpace+file.Size > user.TotalSpace {
		return nil, errors.New("存储空间不足")
	}

	// 创建新文件记录（硬链接，共享存储）
	newFile := &model.File{
		UserID:   userID,
		ParentID: parentID,
		Name:     newName,
		Type:     "file",
		Size:     file.Size,
		MimeType: utils.GetMimeType(newName),
		FilePath: file.FilePath,
		FileHash: file.FileHash,
	}

	err = s.fileRepo.Create(newFile)
	if err != nil {
		return nil, err
	}

	// 更新用户已使用空间
	err = s.userRepo.UpdateUsedSpace(userID, file.Size)
	if err != nil {
		return nil, err
	}

	return newFile.ToResponse(), nil
}

// copyFolder 复制文件夹
func (s *FileService) copyFolder(userID uint, folder *model.File, parentID *uint, newName string) (*model.FileResponse, error) {
	// 创建新文件夹
	newFolder := &model.File{
		UserID:   userID,
		ParentID: parentID,
		Name:     newName,
		Type:     "folder",
		Size:     0,
	}

	err := s.fileRepo.Create(newFolder)
	if err != nil {
		return nil, err
	}

	// 递归复制子文件
	children, err := s.fileRepo.GetChildren(folder.ID)
	if err != nil {
		return nil, err
	}

	for _, child := range children {
		if child.IsFolder() {
			_, err = s.copyFolder(userID, &child, &newFolder.ID, child.Name)
		} else {
			_, err = s.copyFile(userID, &child, &newFolder.ID, child.Name)
		}
		if err != nil {
			return nil, err
		}
	}

	return newFolder.ToResponse(), nil
}

// Delete 删除文件/文件夹（移到回收站）
func (s *FileService) Delete(userID, fileID uint) error {
	file, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("文件不存在")
		}
		return err
	}

	// 递归删除子文件
	if file.IsFolder() {
		err = s.deleteChildren(file.ID)
		if err != nil {
			return err
		}
	}

	// 软删除文件
	return s.fileRepo.Delete(fileID, userID)
}

// deleteChildren 递归删除子文件
func (s *FileService) deleteChildren(parentID uint) error {
	children, err := s.fileRepo.GetChildren(parentID)
	if err != nil {
		return err
	}

	for _, child := range children {
		if child.IsFolder() {
			err = s.deleteChildren(child.ID)
			if err != nil {
				return err
			}
		}
		err = s.fileRepo.Delete(child.ID, child.UserID)
		if err != nil {
			return err
		}
	}

	return nil
}

// Search 搜索文件
func (s *FileService) Search(userID uint, req *model.FileSearchRequest) ([]model.FileResponse, int64, error) {
	files, total, err := s.fileRepo.Search(userID, req.Keyword, req.Type, req.Page, req.PageSize)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]model.FileResponse, len(files))
	for i, file := range files {
		responses[i] = *file.ToResponse()
	}

	return responses, total, nil
}

// GetDeletedFiles 获取回收站文件
func (s *FileService) GetDeletedFiles(userID uint, page, pageSize int) ([]model.FileResponse, int64, error) {
	files, total, err := s.fileRepo.GetDeletedFiles(userID, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	responses := make([]model.FileResponse, len(files))
	for i, file := range files {
		responses[i] = *file.ToResponse()
	}

	return responses, total, nil
}

// Restore 恢复文件
func (s *FileService) Restore(userID, fileID uint) (*model.FileResponse, error) {
	// 这里需要先获取文件信息，包括已删除的
	var file model.File
	err := s.db.Where("id = ? AND user_id = ? AND is_deleted = ?", fileID, userID, true).First(&file).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return nil, errors.New("文件不存在")
		}
		return nil, err
	}

	// 检查恢复位置是否有同名文件
	exists, err := s.fileRepo.ExistsByName(userID, file.ParentID, file.Name, fileID)
	if err != nil {
		return nil, err
	}
	if exists {
		return nil, errors.New("恢复位置已存在同名文件")
	}

	err = s.fileRepo.Restore(fileID, userID)
	if err != nil {
		return nil, err
	}

	// 重新获取文件信息
	restoredFile, err := s.fileRepo.GetByIDWithUser(fileID, userID)
	if err != nil {
		return nil, err
	}

	return restoredFile.ToResponse(), nil
}

// PermanentDelete 永久删除文件
func (s *FileService) PermanentDelete(userID, fileID uint) error {
	// 获取文件信息（包括已删除的）
	var file model.File
	err := s.db.Where("id = ? AND user_id = ? AND is_deleted = ?", fileID, userID, true).First(&file).Error
	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("文件不存在")
		}
		return err
	}

	// 如果是文件，删除物理文件（检查是否还有其他引用）
	if file.IsFile() {
		var count int64
		s.db.Model(&model.File{}).Where("file_hash = ? AND is_deleted = ?", file.FileHash, false).Count(&count)
		if count == 0 {
			// 没有其他引用，删除物理文件
			fullPath := filepath.Join(s.cfg.Storage.BasePath, file.FilePath)
			os.Remove(fullPath)
		}

		// 更新用户已使用空间
		s.userRepo.UpdateUsedSpace(userID, -file.Size)
	}

	// 永久删除数据库记录
	return s.fileRepo.PermanentDelete(fileID, userID)
}
