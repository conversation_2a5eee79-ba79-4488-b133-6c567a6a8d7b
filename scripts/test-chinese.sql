-- 测试中文支持的 SQL 脚本
-- 在 psql 中执行：psql -h localhost -p 5432 -U postgres -d jiangjiang_netdisk -f test-chinese.sql

-- 设置客户端编码
SET client_encoding = 'UTF8';
SET timezone = 'Asia/Shanghai';

-- 测试中文插入和查询
DO $$
BEGIN
    -- 检查是否存在测试表
    IF NOT EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'test_chinese') THEN
        -- 创建测试表
        CREATE TABLE test_chinese (
            id SERIAL PRIMARY KEY,
            chinese_text VARCHAR(255),
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        );
        
        -- 插入中文测试数据
        INSERT INTO test_chinese (chinese_text) VALUES 
            ('江江网盘'),
            ('中文文件名测试'),
            ('用户注册成功'),
            ('文件上传完成'),
            ('分享链接已创建');
        
        RAISE NOTICE '测试表创建成功，已插入中文测试数据';
    ELSE
        RAISE NOTICE '测试表已存在';
    END IF;
END $$;

-- 查询测试数据
SELECT 
    id,
    chinese_text as "中文内容",
    created_at as "创建时间"
FROM test_chinese;

-- 显示数据库编码信息
SELECT 
    'Database Encoding' as "配置项",
    pg_encoding_to_char(encoding) as "值"
FROM pg_database 
WHERE datname = 'jiangjiang_netdisk'

UNION ALL

SELECT 
    'Client Encoding' as "配置项",
    pg_encoding_to_char(pg_char_to_encoding(current_setting('client_encoding'))) as "值"

UNION ALL

SELECT 
    'Server Encoding' as "配置项",
    pg_encoding_to_char(pg_char_to_encoding(current_setting('server_encoding'))) as "值"

UNION ALL

SELECT 
    'TimeZone' as "配置项",
    current_setting('timezone') as "值";

-- 清理测试表（可选）
-- DROP TABLE IF EXISTS test_chinese;
