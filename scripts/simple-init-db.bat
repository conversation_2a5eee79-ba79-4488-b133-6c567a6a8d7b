@echo off
chcp 65001 >nul
echo 🗄️ 简化数据库初始化...

echo 请输入 PostgreSQL 的 postgres 用户密码:
set /p DB_PASSWORD=密码: 

echo.
echo 🏗️ 创建数据库...

:: 使用 PGPASSWORD 环境变量避免交互式密码输入
set PGPASSWORD=%DB_PASSWORD%

:: 创建数据库
psql -h localhost -p 5432 -U postgres -d postgres -c "DROP DATABASE IF EXISTS jiangjiang_netdisk;"
psql -h localhost -p 5432 -U postgres -d postgres -c "CREATE DATABASE jiangjiang_netdisk WITH ENCODING='UTF8';"

if %errorlevel% equ 0 (
    echo ✅ 数据库创建成功
    
    echo 🔧 设置数据库编码...
    psql -h localhost -p 5432 -U postgres -d jiangjiang_netdisk -c "SET client_encoding = 'UTF8';"
    
    echo ✅ 数据库初始化完成
    echo.
    echo 📊 数据库信息：
    echo    主机: localhost:5432
    echo    数据库: jiangjiang_netdisk
    echo    用户名: postgres
    echo    密码: %DB_PASSWORD%
    echo.
    
    :: 将密码写入 .env 文件
    echo 📝 更新配置文件...
    (
        echo SERVER_PORT=8080
        echo GIN_MODE=debug
        echo DB_HOST=localhost
        echo DB_PORT=5432
        echo DB_USER=postgres
        echo DB_PASSWORD=%DB_PASSWORD%
        echo DB_NAME=jiangjiang_netdisk
        echo DB_SSLMODE=disable
        echo JWT_SECRET=jiangjiang-netdisk-super-secret-key-2024
        echo JWT_EXPIRE_HOUR=24
        echo STORAGE_PATH=.\storage
        echo MAX_FILE_SIZE=1073741824
        echo CHUNK_SIZE=5242880
    ) > ..\backend\.env
    
    echo ✅ 配置文件已更新
    
) else (
    echo ❌ 数据库创建失败
    echo 请检查：
    echo 1. PostgreSQL 服务是否运行
    echo 2. 密码是否正确
    echo 3. 是否有创建数据库的权限
)

:: 清除密码环境变量
set PGPASSWORD=

echo.
pause
