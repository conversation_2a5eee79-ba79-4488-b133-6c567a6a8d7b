@echo off
chcp 65001 >nul
echo 🔗 测试数据库连接...

:: 设置数据库连接参数
set PGHOST=localhost
set PGPORT=5432
set PGUSER=postgres

echo 请输入 PostgreSQL 密码:
set /p PGPASSWORD=密码: 

echo.
echo 🔍 测试连接到 PostgreSQL...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "SELECT version();"

if %errorlevel% equ 0 (
    echo ✅ PostgreSQL 连接成功
    
    echo.
    echo 🔍 检查数据库是否存在...
    psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='jiangjiang_netdisk';" | findstr "1" >nul
    
    if %errorlevel% equ 0 (
        echo ✅ 数据库 jiangjiang_netdisk 已存在
        
        echo.
        echo 🔍 测试连接到应用数据库...
        psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d jiangjiang_netdisk -c "SELECT current_database(), current_user, version();"
        
        if %errorlevel% equ 0 (
            echo ✅ 应用数据库连接成功
        ) else (
            echo ❌ 应用数据库连接失败
        )
    ) else (
        echo ⚠️ 数据库 jiangjiang_netdisk 不存在
        echo 请运行 init-db-windows.bat 创建数据库
    )
) else (
    echo ❌ PostgreSQL 连接失败
    echo.
    echo 可能的原因：
    echo 1. PostgreSQL 服务未启动
    echo 2. 用户名或密码错误
    echo 3. 端口 5432 被占用或防火墙阻止
    echo 4. PostgreSQL 未正确安装
    echo.
    echo 解决方法：
    echo 1. 检查服务：services.msc 查找 postgresql 服务
    echo 2. 重置密码：参考文档中的密码重置步骤
    echo 3. 检查端口：netstat -an ^| findstr :5432
)

echo.
pause
