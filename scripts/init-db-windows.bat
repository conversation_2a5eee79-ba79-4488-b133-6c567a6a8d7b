@echo off
chcp 65001 >nul
echo 🗄️ 初始化江江网盘数据库...

:: 检查 PostgreSQL 是否安装
where psql >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL 未安装或 psql 不在 PATH 中
    echo 请确保 PostgreSQL 已安装并添加到系统 PATH
    pause
    exit /b 1
)

:: 设置数据库连接参数
set PGHOST=localhost
set PGPORT=5432
set PGUSER=postgres
set PGPASSWORD=password

echo 请输入 PostgreSQL 的 postgres 用户密码:
set /p PGPASSWORD=密码: 

:: 测试连接
echo 🔗 测试数据库连接...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "SELECT version();" >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ 数据库连接失败，请检查：
    echo    1. PostgreSQL 服务是否启动
    echo    2. 用户名密码是否正确
    echo    3. 端口 5432 是否可访问
    pause
    exit /b 1
)

echo ✅ 数据库连接成功

:: 检查数据库是否已存在
echo 🔍 检查数据库是否存在...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -t -c "SELECT 1 FROM pg_database WHERE datname='jiangjiang_netdisk';" | findstr "1" >nul
if %errorlevel% equ 0 (
    echo ⚠️ 数据库 jiangjiang_netdisk 已存在
    set /p choice=是否删除并重新创建？(y/N): 
    if /i "!choice!"=="y" (
        echo 🗑️ 删除现有数据库...
        psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "DROP DATABASE IF EXISTS jiangjiang_netdisk;"
    ) else (
        echo 📋 使用现有数据库
        goto :end
    )
)

:: 创建数据库
echo 🏗️ 创建数据库...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d postgres -c "CREATE DATABASE jiangjiang_netdisk WITH OWNER = postgres ENCODING = 'UTF8' LC_COLLATE = 'C' LC_CTYPE = 'C' TABLESPACE = pg_default CONNECTION LIMIT = -1;"

if %errorlevel% equ 0 (
    echo ✅ 数据库创建成功
) else (
    echo ❌ 数据库创建失败
    pause
    exit /b 1
)

:: 设置数据库编码
echo 🔧 配置数据库编码...
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d jiangjiang_netdisk -c "SET client_encoding = 'UTF8';"
psql -h %PGHOST% -p %PGPORT% -U %PGUSER% -d jiangjiang_netdisk -c "SET timezone = 'Asia/Shanghai';"

:end
echo.
echo ✅ 数据库初始化完成！
echo.
echo 📊 数据库信息：
echo    主机: %PGHOST%:%PGPORT%
echo    数据库: jiangjiang_netdisk
echo    用户名: %PGUSER%
echo    编码: UTF8
echo    时区: Asia/Shanghai
echo.
echo 🚀 现在可以启动江江网盘了！

pause
