#!/bin/bash

# 江江网盘启动脚本

echo "🚀 启动江江网盘..."

# 检查 Docker 是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker 未安装，请先安装 Docker"
    exit 1
fi

# 检查 Docker Compose 是否安装
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose 未安装，请先安装 Docker Compose"
    exit 1
fi

# 创建必要的目录
echo "📁 创建存储目录..."
mkdir -p backend/storage
mkdir -p logs

# 设置权限
chmod 755 backend/storage

# 启动服务
echo "🐳 启动 Docker 容器..."
docker-compose up -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "✅ 江江网盘启动成功！"
echo ""
echo "🌐 访问地址："
echo "   前端: http://localhost"
echo "   后端API: http://localhost:8080"
echo ""
echo "📊 数据库信息："
echo "   主机: localhost:5432"
echo "   数据库: jiangjiang_netdisk"
echo "   用户名: postgres"
echo "   密码: password"
echo ""
echo "📝 查看日志："
echo "   docker-compose logs -f"
echo ""
echo "🛑 停止服务："
echo "   docker-compose down"
echo ""

# 检查前端是否可访问
echo "🔗 检查前端服务..."
if curl -s http://localhost > /dev/null; then
    echo "✅ 前端服务正常"
else
    echo "⚠️  前端服务可能还在启动中，请稍等片刻"
fi

# 检查后端是否可访问
echo "🔗 检查后端服务..."
if curl -s http://localhost:8080/api/health > /dev/null; then
    echo "✅ 后端服务正常"
else
    echo "⚠️  后端服务可能还在启动中，请稍等片刻"
fi

echo ""
echo "🎉 享受使用江江网盘吧！"
