@echo off
chcp 65001 >nul
echo 🚀 启动江江网盘...

:: 检查 Go 是否安装
where go >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Go 未安装，请先安装 Go 1.21+
    echo 下载地址: https://golang.org/dl/
    pause
    exit /b 1
)

:: 检查 Node.js 是否安装
where node >nul 2>nul
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js 18+
    echo 下载地址: https://nodejs.org/
    pause
    exit /b 1
)

:: 检查 PostgreSQL 是否运行
netstat -an | findstr :5432 >nul
if %errorlevel% neq 0 (
    echo ❌ PostgreSQL 未运行，请先启动 PostgreSQL 服务
    echo 或检查端口 5432 是否被占用
    pause
    exit /b 1
)

:: 创建存储目录
echo 📁 创建存储目录...
if not exist "backend\storage" mkdir "backend\storage"

:: 启动后端服务
echo 🔧 启动后端服务...
start "江江网盘-后端" cmd /k "cd /d %~dp0\..\backend && go run cmd/server/main.go"

:: 等待后端启动
echo ⏳ 等待后端服务启动...
timeout /t 5 /nobreak >nul

:: 检查后端是否启动成功
powershell -Command "try { Invoke-WebRequest -Uri 'http://localhost:8080' -TimeoutSec 5 -ErrorAction Stop | Out-Null; exit 0 } catch { exit 1 }" >nul 2>nul
if %errorlevel% equ 0 (
    echo ✅ 后端服务启动成功
) else (
    echo ⚠️ 后端服务可能还在启动中...
)

:: 启动前端服务
echo 🎨 启动前端服务...
start "江江网盘-前端" cmd /k "cd /d %~dp0\..\frontend && npm run dev"

:: 等待前端启动
echo ⏳ 等待前端服务启动...
timeout /t 10 /nobreak >nul

echo.
echo ✅ 江江网盘启动完成！
echo.
echo 🌐 访问地址：
echo    前端: http://localhost:3000
echo    后端API: http://localhost:8080
echo.
echo 📊 数据库信息：
echo    主机: localhost:5432
echo    数据库: jiangjiang_netdisk
echo    用户名: postgres
echo.
echo 🛑 停止服务：关闭对应的命令行窗口
echo.
echo 🎉 享受使用江江网盘吧！

pause
