version: '3.8'

services:
  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: jiangjiang-postgres
    environment:
      POSTGRES_DB: jiangjiang_netdisk
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - jiangjiang-network
    restart: unless-stopped

  # Redis (可选，用于缓存)
  redis:
    image: redis:7-alpine
    container_name: jiangjiang-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - jiangjiang-network
    restart: unless-stopped

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: jiangjiang-backend
    environment:
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_USER=postgres
      - DB_PASSWORD=password
      - DB_NAME=jiangjiang_netdisk
      - DB_SSLMODE=disable
      - SERVER_PORT=8080
      - GIN_MODE=release
      - JWT_SECRET=your-super-secret-jwt-key
      - STORAGE_PATH=/app/storage
    ports:
      - "8080:8080"
    volumes:
      - ./backend/storage:/app/storage
    depends_on:
      - postgres
    networks:
      - jiangjiang-network
    restart: unless-stopped

  # 前端服务 (Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: jiangjiang-frontend
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
    depends_on:
      - backend
    networks:
      - jiangjiang-network
    restart: unless-stopped

volumes:
  postgres_data:
  redis_data:

networks:
  jiangjiang-network:
    driver: bridge
