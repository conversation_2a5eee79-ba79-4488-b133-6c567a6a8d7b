# 江江网盘 - 私人云盘系统开发文档

## 项目概述

江江网盘是一个类似百度网盘的私人云盘系统，提供文件上传、下载、管理、分享等功能。

### 技术栈
- **前端**: Vue 3 + TypeScript + Element Plus + Vite
- **后端**: Go + Gin + GORM
- **数据库**: PostgreSQL
- **文件存储**: 本地存储 + 可扩展云存储
- **认证**: JWT Token

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Vue Frontend  │────│  Go Backend API │────│   PostgreSQL    │
│                 │    │                 │    │                 │
│  - 文件管理界面  │    │  - RESTful API  │    │  - 用户数据     │
│  - 用户认证     │    │  - 文件处理     │    │  - 文件元数据   │
│  - 文件预览     │    │  - 权限控制     │    │  - 分享记录     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                       ┌─────────────────┐
                       │   File Storage  │
                       │                 │
                       │  - 本地存储     │
                       │  - 文件分块     │
                       │  - 断点续传     │
                       └─────────────────┘
```

## 功能模块

### 1. 用户管理模块
- 用户注册/登录
- 用户信息管理
- 存储空间管理
- 权限控制

### 2. 文件管理模块
- 文件上传（支持大文件分块上传）
- 文件下载（支持断点续传）
- 文件夹创建/删除
- 文件重命名/移动/复制
- 文件搜索
- 回收站功能

### 3. 文件分享模块
- 生成分享链接
- 设置分享权限（密码、有效期）
- 分享文件访问统计

### 4. 文件预览模块
- 图片预览
- 视频播放
- 文档在线预览
- 音频播放

## 数据库设计

### 用户表 (users)
```sql
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    nickname VARCHAR(100),
    avatar_url VARCHAR(255),
    total_space BIGINT DEFAULT 10737418240, -- 10GB
    used_space BIGINT DEFAULT 0,
    status INTEGER DEFAULT 1, -- 1:正常 0:禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 文件表 (files)
```sql
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    parent_id INTEGER REFERENCES files(id), -- 父文件夹ID，NULL表示根目录
    name VARCHAR(255) NOT NULL,
    type VARCHAR(10) NOT NULL, -- 'file' or 'folder'
    size BIGINT DEFAULT 0,
    mime_type VARCHAR(100),
    file_path VARCHAR(500), -- 实际存储路径
    file_hash VARCHAR(64), -- 文件MD5哈希，用于去重
    is_deleted BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP
);
```

### 分享表 (shares)
```sql
CREATE TABLE shares (
    id SERIAL PRIMARY KEY,
    file_id INTEGER REFERENCES files(id),
    user_id INTEGER REFERENCES users(id),
    share_code VARCHAR(20) UNIQUE NOT NULL,
    password VARCHAR(20),
    expire_time TIMESTAMP,
    download_count INTEGER DEFAULT 0,
    view_count INTEGER DEFAULT 0,
    is_active BOOLEAN DEFAULT TRUE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## API 接口设计

### 认证相关
- `POST /api/auth/register` - 用户注册
- `POST /api/auth/login` - 用户登录
- `POST /api/auth/logout` - 用户登出
- `GET /api/auth/profile` - 获取用户信息
- `PUT /api/auth/profile` - 更新用户信息

### 文件管理
- `GET /api/files` - 获取文件列表
- `POST /api/files/upload` - 文件上传
- `GET /api/files/:id/download` - 文件下载
- `POST /api/files/folder` - 创建文件夹
- `PUT /api/files/:id` - 重命名文件/文件夹
- `DELETE /api/files/:id` - 删除文件/文件夹
- `POST /api/files/:id/move` - 移动文件
- `POST /api/files/:id/copy` - 复制文件
- `GET /api/files/search` - 搜索文件

### 分享功能
- `POST /api/shares` - 创建分享
- `GET /api/shares/:code` - 获取分享信息
- `GET /api/shares/:code/files` - 获取分享文件列表
- `GET /api/shares/:code/download/:fileId` - 下载分享文件

## 项目结构

```
jiangjiangNetdisk/
├── backend/                 # Go后端
│   ├── cmd/
│   │   └── server/
│   │       └── main.go
│   ├── internal/
│   │   ├── config/         # 配置管理
│   │   ├── handler/        # HTTP处理器
│   │   ├── middleware/     # 中间件
│   │   ├── model/          # 数据模型
│   │   ├── repository/     # 数据访问层
│   │   ├── service/        # 业务逻辑层
│   │   └── utils/          # 工具函数
│   ├── storage/            # 文件存储目录
│   ├── go.mod
│   └── go.sum
├── frontend/               # Vue前端
│   ├── src/
│   │   ├── components/     # 组件
│   │   ├── views/          # 页面
│   │   ├── router/         # 路由
│   │   ├── store/          # 状态管理
│   │   ├── api/            # API接口
│   │   ├── utils/          # 工具函数
│   │   └── assets/         # 静态资源
│   ├── package.json
│   └── vite.config.ts
├── docs/                   # 文档
├── scripts/                # 脚本文件
└── README.md
```

## 开发环境搭建

### 后端环境
1. 安装 Go 1.19+
2. 安装 PostgreSQL 13+
3. 创建数据库：`createdb jiangjiang_netdisk`

### 前端环境
1. 安装 Node.js 16+
2. 安装 pnpm：`npm install -g pnpm`

## 核心功能实现要点

### 1. 大文件上传
- 使用分块上传技术
- 支持断点续传
- 文件去重（基于MD5）

### 2. 权限控制
- JWT Token认证
- 文件访问权限验证
- 分享权限控制

### 3. 文件存储
- 按用户ID分目录存储
- 文件路径加密
- 支持文件压缩

### 4. 性能优化
- 数据库索引优化
- 文件缓存策略
- 分页查询
- 图片缩略图生成

## 安全考虑

1. **文件上传安全**
   - 文件类型验证
   - 文件大小限制
   - 恶意文件检测

2. **访问控制**
   - JWT Token验证
   - 文件权限检查
   - 防止路径遍历攻击

3. **数据安全**
   - 密码加密存储
   - 敏感信息脱敏
   - SQL注入防护

## 部署方案

### 开发环境
- 使用 Docker Compose 一键部署
- 热重载开发

### 生产环境
- 使用 Nginx 反向代理
- PostgreSQL 主从复制
- 文件存储使用云存储服务

## 后续扩展

1. **移动端支持**
   - 开发移动端APP
   - 响应式设计优化

2. **协作功能**
   - 多人协作编辑
   - 文件版本控制
   - 评论功能

3. **企业功能**
   - 团队空间
   - 权限管理
   - 审计日志

## 快速开始

### 环境要求

- Docker 20.0+
- Docker Compose 2.0+
- Node.js 16+ (开发环境)
- Go 1.21+ (开发环境)
- PostgreSQL 13+ (开发环境)

### 一键启动

```bash
# 克隆项目
git clone <repository-url>
cd jiangjiangNetdisk

# 启动服务
chmod +x scripts/start.sh
./scripts/start.sh
```

### 手动启动

#### 1. 启动数据库
```bash
docker-compose up -d postgres
```

#### 2. 启动后端
```bash
cd backend
go mod tidy
go run cmd/server/main.go
```

#### 3. 启动前端
```bash
cd frontend
npm install
npm run dev
```

### 访问地址

- 前端: http://localhost:3000 (开发) / http://localhost (生产)
- 后端API: http://localhost:8080
- 数据库: localhost:5432

### 默认账号

系统启动后，您可以通过注册页面创建新账号，或使用以下测试账号：

- 用户名: admin
- 密码: 123456

## 功能特性

### ✅ 已完成功能

- 🔐 用户注册/登录/登出
- 📁 文件夹创建/重命名/删除
- 📄 文件上传/下载/重命名/删除
- 🗂️ 文件列表展示（列表/网格视图）
- 🔍 文件搜索
- 📤 文件分享（链接+密码）
- 🗑️ 回收站功能
- 💾 存储空间管理
- 📱 响应式设计
- 🎨 美观的UI界面

### 🚧 开发中功能

- 📊 文件预览
- 🎵 音视频播放
- 📸 图片分类
- 📝 文档分类
- ⏰ 最近使用
- 🔄 断点续传
- 📦 批量操作
- 👥 多用户协作

## 技术亮点

### 前端技术栈
- **Vue 3** - 渐进式JavaScript框架
- **TypeScript** - 类型安全
- **Element Plus** - 企业级UI组件库
- **Pinia** - 状态管理
- **Vue Router** - 路由管理
- **Vite** - 快速构建工具
- **Axios** - HTTP客户端

### 后端技术栈
- **Go** - 高性能编程语言
- **Gin** - 轻量级Web框架
- **GORM** - ORM框架
- **PostgreSQL** - 关系型数据库
- **JWT** - 身份认证
- **Docker** - 容器化部署

### 设计特色
- 🎨 **现代化UI设计** - 采用渐变色彩和毛玻璃效果
- 📱 **响应式布局** - 完美适配桌面和移动设备
- 🌈 **丰富的交互动画** - 提升用户体验
- 🔧 **组件化开发** - 高度可复用的组件设计
- 🚀 **性能优化** - 懒加载、虚拟滚动等优化技术

## 部署指南

### Docker 部署（推荐）

```bash
# 生产环境部署
docker-compose -f docker-compose.yml up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 手动部署

#### 后端部署
```bash
cd backend
go build -o jiangjiang-netdisk cmd/server/main.go
./jiangjiang-netdisk
```

#### 前端部署
```bash
cd frontend
npm run build
# 将 dist 目录部署到 Web 服务器
```

## 开发指南

### 目录结构说明

```
jiangjiangNetdisk/
├── backend/                 # Go后端
│   ├── cmd/server/         # 主程序入口
│   ├── internal/           # 内部包
│   │   ├── config/        # 配置管理
│   │   ├── handler/       # HTTP处理器
│   │   ├── middleware/    # 中间件
│   │   ├── model/         # 数据模型
│   │   ├── repository/    # 数据访问层
│   │   ├── service/       # 业务逻辑层
│   │   └── utils/         # 工具函数
│   └── storage/           # 文件存储目录
├── frontend/               # Vue前端
│   ├── src/
│   │   ├── components/    # 组件
│   │   ├── views/         # 页面
│   │   ├── router/        # 路由
│   │   ├── store/         # 状态管理
│   │   ├── api/           # API接口
│   │   ├── utils/         # 工具函数
│   │   └── types/         # 类型定义
│   └── public/            # 静态资源
├── docs/                  # 文档
├── scripts/               # 脚本文件
└── docker-compose.yml     # Docker编排文件
```

### 开发规范

#### 前端开发规范
- 使用 TypeScript 进行类型检查
- 组件命名采用 PascalCase
- 文件命名采用 kebab-case
- 使用 Composition API
- 遵循 Vue 3 最佳实践

#### 后端开发规范
- 遵循 Go 官方代码规范
- 使用依赖注入模式
- 分层架构设计
- 错误处理规范
- 单元测试覆盖

## 配置说明

### 环境变量

#### 后端环境变量
```bash
# 服务器配置
SERVER_PORT=8080
GIN_MODE=release

# 数据库配置
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=password
DB_NAME=jiangjiang_netdisk
DB_SSLMODE=disable

# JWT配置
JWT_SECRET=your-super-secret-jwt-key
JWT_EXPIRE_HOUR=24

# 存储配置
STORAGE_PATH=./storage
MAX_FILE_SIZE=1073741824  # 1GB
CHUNK_SIZE=5242880        # 5MB
```

#### 前端环境变量
```bash
# API地址
VITE_API_BASE_URL=/api

# 应用配置
VITE_APP_TITLE=江江网盘
VITE_APP_VERSION=1.0.0
```

## 常见问题

### Q: 如何修改文件上传大小限制？
A: 修改后端配置中的 `MAX_FILE_SIZE` 环境变量，同时需要修改 nginx 配置中的 `client_max_body_size`。

### Q: 如何备份数据？
A: 使用 `docker-compose exec postgres pg_dump` 命令备份数据库，同时备份 `backend/storage` 目录中的文件。

### Q: 如何扩展存储空间？
A: 可以通过修改用户表中的 `total_space` 字段来调整用户存储空间限制。

### Q: 如何启用HTTPS？
A: 修改 nginx 配置，添加SSL证书配置，并将 docker-compose.yml 中的443端口映射启用。

## 贡献指南

1. Fork 本仓库
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 联系方式

- 项目地址: [GitHub Repository]
- 问题反馈: [GitHub Issues]
- 邮箱: <EMAIL>

---

**江江网盘** - 让文件管理更简单、更安全、更美观！ 🚀
